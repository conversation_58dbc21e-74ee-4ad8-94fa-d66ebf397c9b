import AdminLayout from '@/layouts/admin-layout';
import { Head } from '@inertiajs/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Search, Filter, Download, Eye, LogIn, User, Shield, Users, Trash2 } from 'lucide-react';
import { useState } from 'react';
import { router } from '@inertiajs/react';

interface LoginLog {
    id: number;
    user_id: number;
    login_at: string;
    ip_address: string;
    user_agent: string;
    login_method: string;
    session_id?: string;
    created_at: string;
    user: {
        id: number;
        name: string;
        email: string;
        role: string;
    };
}

interface LoginLogsProps {
    loginLogs: {
        data: LoginLog[];
        current_page: number;
        last_page: number;
        total: number;
    };
}

export default function LoginLogs({ loginLogs }: LoginLogsProps) {
    const [searchTerm, setSearchTerm] = useState('');
    const [roleFilter, setRoleFilter] = useState('all');
    const [methodFilter, setMethodFilter] = useState('all');

    // 麵包屑導航
    const breadcrumbs = [
        { title: '儀表板', href: '/dashboard' },
        { title: '登入記錄', href: '/admin/login-logs' },
    ];

    // 角色徽章
    const getRoleBadge = (role: string) => {
        switch (role) {
            case 'admin':
                return (
                    <Badge variant="secondary" className="flex items-center gap-1 bg-red-100 text-red-800">
                        <Shield className="h-3 w-3" />
                        管理員
                    </Badge>
                );
            case 'applicant':
                return (
                    <Badge variant="secondary" className="flex items-center gap-1 bg-blue-100 text-blue-800">
                        <User className="h-3 w-3" />
                        申請人
                    </Badge>
                );
            case 'recommender':
                return (
                    <Badge variant="secondary" className="flex items-center gap-1 bg-green-100 text-green-800">
                        <Users className="h-3 w-3" />
                        推薦人
                    </Badge>
                );
            default:
                return <Badge variant="secondary">{role}</Badge>;
        }
    };

    // 登入方式徽章
    const getMethodBadge = (method: string | undefined | null) => {
        if (!method) {
            return <Badge variant="outline">未知</Badge>;
        }
        switch (method.toLowerCase()) {
            case 'password':
                return (
                    <Badge variant="outline" className="text-blue-700">
                        密碼登入
                    </Badge>
                );
            case 'token':
                return (
                    <Badge variant="outline" className="text-green-700">
                        Token 登入
                    </Badge>
                );
            case 'sso':
                return (
                    <Badge variant="outline" className="text-purple-700">
                        SSO 登入
                    </Badge>
                );
            default:
                return <Badge variant="outline">{method}</Badge>;
        }
    };

    // 過濾登入記錄
    const filteredLogs = loginLogs.data.filter((log) => {
        const matchesSearch =
            log.user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            log.user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
            log.ip_address.includes(searchTerm);

        const matchesRole = roleFilter === 'all' || log.user.role === roleFilter;
        const matchesMethod = methodFilter === 'all' || log.login_method?.toLowerCase() === methodFilter;

        return matchesSearch && matchesRole && matchesMethod;
    });

    // 獲取所有登入方式
    const methods = Array.from(
        new Set(loginLogs.data.map((log) => log.login_method?.toLowerCase() || 'unknown').filter((method) => method !== 'unknown')),
    );

    // 獲取瀏覽器資訊
    const getBrowserInfo = (userAgent: string) => {
        // 簡單的瀏覽器檢測
        if (userAgent.includes('Chrome')) return 'Chrome';
        if (userAgent.includes('Firefox')) return 'Firefox';
        if (userAgent.includes('Safari')) return 'Safari';
        if (userAgent.includes('Edge')) return 'Edge';
        return '未知瀏覽器';
    };

    // 匯出登入記錄
    const handleExportLogs = () => {
        const params = new URLSearchParams();
        if (roleFilter !== 'all') params.append('role', roleFilter);
        if (methodFilter !== 'all') params.append('method', methodFilter);
        if (searchTerm) params.append('search', searchTerm);

        window.open(`/admin/login-logs/export?${params.toString()}`, '_blank');
    };

    // 清理舊記錄
    const handleCleanupLogs = () => {
        if (confirm('確定要清理 90 天前的舊登入記錄嗎？此操作無法復原。')) {
            router.post(
                '/admin/login-logs/cleanup',
                { days: 90 },
                {
                    onSuccess: () => {
                        alert('舊記錄已清理完成');
                        router.reload();
                    },
                    onError: () => {
                        alert('清理失敗，請重試');
                    },
                },
            );
        }
    };

    return (
        <AdminLayout breadcrumbs={breadcrumbs} title="登入記錄" description="查看和管理使用者登入記錄和安全日誌">
            <Head title="登入記錄" />

            <div className="space-y-6 p-6">
                {/* 篩選和搜尋區域 */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <Filter className="h-5 w-5" />
                            篩選和搜尋
                        </CardTitle>
                        <CardDescription>使用下方工具來篩選和搜尋登入記錄</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
                            <div className="relative">
                                <Search className="absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 transform text-gray-400" />
                                <Input
                                    placeholder="搜尋使用者、信箱或 IP..."
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                    className="pl-10"
                                />
                            </div>
                            <Select value={roleFilter} onValueChange={setRoleFilter}>
                                <SelectTrigger>
                                    <SelectValue placeholder="選擇角色" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">所有角色</SelectItem>
                                    <SelectItem value="admin">管理員</SelectItem>
                                    <SelectItem value="applicant">申請人</SelectItem>
                                    <SelectItem value="recommender">推薦人</SelectItem>
                                </SelectContent>
                            </Select>
                            <Select value={methodFilter} onValueChange={setMethodFilter}>
                                <SelectTrigger>
                                    <SelectValue placeholder="登入方式" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">所有方式</SelectItem>
                                    {methods.map((method) => (
                                        <SelectItem key={method} value={method}>
                                            {method === 'password'
                                                ? '密碼登入'
                                                : method === 'token'
                                                  ? 'Token 登入'
                                                  : method === 'sso'
                                                    ? 'SSO 登入'
                                                    : method}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                            <div className="flex gap-2">
                                <Button variant="outline" size="sm" className="flex items-center gap-1" onClick={handleExportLogs}>
                                    <Download className="h-3 w-3" />
                                    <span className="text-xs">匯出記錄</span>
                                </Button>
                                <Button
                                    variant="outline"
                                    size="sm"
                                    className="flex items-center gap-1 text-red-600 hover:text-red-700"
                                    onClick={handleCleanupLogs}
                                >
                                    <Trash2 className="h-3 w-3" />
                                    <span className="text-xs">清理舊記錄</span>
                                </Button>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* 統計資訊 */}
                <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center gap-2">
                                <LogIn className="h-5 w-5 text-gray-500" />
                                <div>
                                    <div className="text-2xl font-bold">{loginLogs.total}</div>
                                    <p className="text-xs text-muted-foreground">總登入次數</p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardContent className="p-6">
                            <div className="text-2xl font-bold text-red-600">{loginLogs.data.filter((log) => log.user.role === 'admin').length}</div>
                            <p className="text-xs text-muted-foreground">管理員登入</p>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardContent className="p-6">
                            <div className="text-2xl font-bold text-blue-600">
                                {loginLogs.data.filter((log) => log.user.role === 'applicant').length}
                            </div>
                            <p className="text-xs text-muted-foreground">申請人登入</p>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardContent className="p-6">
                            <div className="text-2xl font-bold text-green-600">
                                {loginLogs.data.filter((log) => log.user.role === 'recommender').length}
                            </div>
                            <p className="text-xs text-muted-foreground">推薦人登入</p>
                        </CardContent>
                    </Card>
                </div>

                {/* 登入記錄列表 */}
                <Card>
                    <CardHeader>
                        <CardTitle>登入記錄</CardTitle>
                        <CardDescription>
                            顯示 {filteredLogs.length} / {loginLogs.data.length} 筆記錄
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            {filteredLogs.map((log) => (
                                <div key={log.id} className="flex items-center justify-between rounded-lg border p-4 hover:bg-gray-50">
                                    <div className="grid flex-1 grid-cols-1 gap-4 md:grid-cols-5">
                                        <div>
                                            <p className="font-medium">{log.user.name}</p>
                                            <p className="text-sm text-gray-500">{log.user.email}</p>
                                        </div>
                                        <div>{getRoleBadge(log.user.role)}</div>
                                        <div>
                                            {getMethodBadge(log.login_method)}
                                            <p className="mt-1 text-sm text-gray-500">{getBrowserInfo(log.user_agent)}</p>
                                        </div>
                                        <div>
                                            <p className="text-sm text-gray-500">IP 位址</p>
                                            <p className="text-sm font-medium">{log.ip_address}</p>
                                        </div>
                                        <div>
                                            <p className="text-sm text-gray-500">登入時間</p>
                                            <p className="text-sm font-medium">{new Date(log.login_at).toLocaleString('zh-TW')}</p>
                                        </div>
                                    </div>
                                </div>
                            ))}

                            {filteredLogs.length === 0 && <div className="py-8 text-center text-gray-500">沒有找到符合條件的登入記錄</div>}
                        </div>
                    </CardContent>
                </Card>

                {/* 安全提醒 */}
                <Card className="border-blue-200 bg-blue-50">
                    <CardContent>
                        <div className="flex items-start gap-3">
                            <Shield className="mt-0.5 h-5 w-5 text-blue-600" />
                            <div className="space-y-1">
                                <p className="text-sm font-medium text-blue-800">安全提醒</p>
                                <p className="text-sm text-blue-700">
                                    定期檢查登入記錄有助於發現異常登入行為。如發現可疑活動，請立即採取安全措施。
                                    建議定期清理舊的登入記錄以維護系統效能。
                                </p>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </AdminLayout>
    );
}
