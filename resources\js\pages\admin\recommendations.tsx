import AdminLayout from '@/layouts/admin-layout';
import { Head } from '@inertiajs/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Search, Filter, Download, Eye } from 'lucide-react';
import { useState } from 'react';

interface Recommendation {
    id: number;
    applicant_id: number;
    application_form_id: string;
    department_name: string;
    program_type: string;
    status: string;
    recommender_name?: string;
    recommender_email?: string;
    submitted_at?: string;
    created_at: string;
    applicant?: {
        user?: {
            name: string;
            email: string;
        };
    };
}

interface RecommendationsProps {
    recommendations: Recommendation[];
}

export default function Recommendations({ recommendations }: RecommendationsProps) {
    const [searchTerm, setSearchTerm] = useState('');
    const [statusFilter, setStatusFilter] = useState('all');
    const [departmentFilter, setDepartmentFilter] = useState('all');

    // 麵包屑導航
    const breadcrumbs = [
        { title: '儀表板', href: '/dashboard' },
        { title: '推薦函管理', href: '/admin/recommendations' },
    ];

    // 狀態徽章
    const getStatusBadge = (status: string) => {
        switch (status) {
            case 'pending':
                return (
                    <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
                        待處理
                    </Badge>
                );
            case 'submitted':
                return (
                    <Badge variant="secondary" className="bg-green-100 text-green-800">
                        已提交
                    </Badge>
                );
            case 'declined':
                return (
                    <Badge variant="secondary" className="bg-red-100 text-red-800">
                        已婉拒
                    </Badge>
                );
            case 'withdrawn':
                return (
                    <Badge variant="secondary" className="bg-gray-100 text-gray-800">
                        已撤回
                    </Badge>
                );
            default:
                return <Badge variant="secondary">{status}</Badge>;
        }
    };

    // 過濾推薦函
    const filteredRecommendations = recommendations.filter((rec) => {
        const matchesSearch =
            rec.applicant?.user?.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
            rec.recommender_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
            rec.department_name.toLowerCase().includes(searchTerm.toLowerCase());

        const matchesStatus = statusFilter === 'all' || rec.status === statusFilter;
        const matchesDepartment = departmentFilter === 'all' || rec.department_name === departmentFilter;

        return matchesSearch && matchesStatus && matchesDepartment;
    });

    // 獲取所有系所
    const departments = Array.from(new Set(recommendations.map((rec) => rec.department_name)));

    return (
        <AdminLayout breadcrumbs={breadcrumbs} title="推薦函管理" description="查看和管理所有推薦函申請">
            <Head title="推薦函管理" />

            <div className="space-y-6 p-6">
                {/* 篩選和搜尋區域 */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <Filter className="h-5 w-5" />
                            篩選和搜尋
                        </CardTitle>
                        <CardDescription>使用下方工具來篩選和搜尋推薦函</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
                            <div className="relative">
                                <Search className="absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 transform text-gray-400" />
                                <Input
                                    placeholder="搜尋申請人、推薦人或系所..."
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                    className="pl-10"
                                />
                            </div>
                            <Select value={statusFilter} onValueChange={setStatusFilter}>
                                <SelectTrigger>
                                    <SelectValue placeholder="選擇狀態" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">所有狀態</SelectItem>
                                    <SelectItem value="pending">待處理</SelectItem>
                                    <SelectItem value="submitted">已提交</SelectItem>
                                    <SelectItem value="declined">已婉拒</SelectItem>
                                    <SelectItem value="withdrawn">已撤回</SelectItem>
                                </SelectContent>
                            </Select>
                            <Select value={departmentFilter} onValueChange={setDepartmentFilter}>
                                <SelectTrigger>
                                    <SelectValue placeholder="選擇系所" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">所有系所</SelectItem>
                                    {departments.map((dept) => (
                                        <SelectItem key={dept} value={dept}>
                                            {dept}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                            <Button variant="outline" size="sm" className="flex items-center gap-1">
                                <Download className="h-3 w-3" />
                                <span className="text-xs">匯出資料</span>
                            </Button>
                        </div>
                    </CardContent>
                </Card>

                {/* 統計資訊 */}
                <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
                    <Card>
                        <CardContent className="p-6">
                            <div className="text-2xl font-bold">{recommendations.length}</div>
                            <p className="text-xs text-muted-foreground">總推薦函數</p>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardContent className="p-6">
                            <div className="text-2xl font-bold text-green-600">{recommendations.filter((r) => r.status === 'submitted').length}</div>
                            <p className="text-xs text-muted-foreground">已提交</p>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardContent className="p-6">
                            <div className="text-2xl font-bold text-yellow-600">{recommendations.filter((r) => r.status === 'pending').length}</div>
                            <p className="text-xs text-muted-foreground">待處理</p>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardContent className="p-6">
                            <div className="text-2xl font-bold text-red-600">{recommendations.filter((r) => r.status === 'declined').length}</div>
                            <p className="text-xs text-muted-foreground">已婉拒</p>
                        </CardContent>
                    </Card>
                </div>

                {/* 推薦函列表 */}
                <Card>
                    <CardHeader>
                        <CardTitle>推薦函列表</CardTitle>
                        <CardDescription>
                            顯示 {filteredRecommendations.length} / {recommendations.length} 筆推薦函
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            {filteredRecommendations.map((rec) => (
                                <div key={rec.id} className="flex items-center justify-between rounded-lg border p-4 hover:bg-gray-50">
                                    <div className="grid flex-1 grid-cols-1 gap-4 md:grid-cols-4">
                                        <div>
                                            <p className="font-medium">{rec.applicant?.user?.name || '未知申請人'}</p>
                                            <p className="text-sm text-gray-500">{rec.applicant?.user?.email}</p>
                                        </div>
                                        <div>
                                            <p className="font-medium">{rec.recommender_name || '未指定'}</p>
                                            <p className="text-sm text-gray-500">{rec.recommender_email}</p>
                                        </div>
                                        <div>
                                            <p className="font-medium">{rec.department_name}</p>
                                            <p className="text-sm text-gray-500">{rec.program_type}</p>
                                        </div>
                                        <div>
                                            {getStatusBadge(rec.status)}
                                            <p className="mt-1 text-sm text-gray-500">
                                                {rec.submitted_at
                                                    ? `提交於 ${new Date(rec.submitted_at).toLocaleDateString()}`
                                                    : `建立於 ${new Date(rec.created_at).toLocaleDateString()}`}
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            ))}

                            {filteredRecommendations.length === 0 && <div className="py-8 text-center text-gray-500">沒有找到符合條件的推薦函</div>}
                        </div>
                    </CardContent>
                </Card>
            </div>
        </AdminLayout>
    );
}
