import AdminLayout from '@/layouts/admin-layout';
import { Head } from '@inertiajs/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Search, Filter, Download, Eye, Mail, Trash2 } from 'lucide-react';
import { useState } from 'react';
import { router } from '@inertiajs/react';

interface EmailLog {
    id: number;
    recommendation_letter_id: number;
    email_type: string;
    recipient_email: string;
    subject: string;
    status: string;
    sent_at: string;
    error_message?: string;
    created_at: string;
    recommendationLetter?: {
        department_name: string;
        program_type: string;
    };
}

interface EmailLogsProps {
    emailLogs: {
        data: EmailLog[];
        current_page: number;
        last_page: number;
        total: number;
    };
}

export default function EmailLogs({ emailLogs }: EmailLogsProps) {
    const [searchTerm, setSearchTerm] = useState('');
    const [statusFilter, setStatusFilter] = useState('all');
    const [typeFilter, setTypeFilter] = useState('all');

    // 麵包屑導航
    const breadcrumbs = [
        { title: '儀表板', href: '/dashboard' },
        { title: 'Email 管理', href: '/admin/email-logs' },
    ];

    // 狀態徽章
    const getStatusBadge = (status: string) => {
        switch (status) {
            case 'sent':
                return (
                    <Badge variant="secondary" className="bg-green-100 text-green-800">
                        已發送
                    </Badge>
                );
            case 'failed':
                return (
                    <Badge variant="secondary" className="bg-red-100 text-red-800">
                        發送失敗
                    </Badge>
                );
            case 'pending':
                return (
                    <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
                        待發送
                    </Badge>
                );
            default:
                return <Badge variant="secondary">{status}</Badge>;
        }
    };

    // Email 類型徽章
    const getTypeBadge = (type: string) => {
        switch (type) {
            case 'invitation':
                return (
                    <Badge variant="outline" className="text-blue-700">
                        邀請信
                    </Badge>
                );
            case 'reminder':
                return (
                    <Badge variant="outline" className="text-orange-700">
                        提醒信
                    </Badge>
                );
            case 'submitted':
                return (
                    <Badge variant="outline" className="text-green-700">
                        完成通知
                    </Badge>
                );
            case 'declined':
                return (
                    <Badge variant="outline" className="text-red-700">
                        婉拒通知
                    </Badge>
                );
            default:
                return <Badge variant="outline">{type}</Badge>;
        }
    };

    // 過濾 Email 日誌
    const filteredLogs = emailLogs.data.filter((log) => {
        const matchesSearch =
            log.recipient_email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
            log.subject?.toLowerCase().includes(searchTerm.toLowerCase()) ||
            log.recommendationLetter?.department_name?.toLowerCase().includes(searchTerm.toLowerCase());

        const matchesStatus = statusFilter === 'all' || log.status === statusFilter;
        const matchesType = typeFilter === 'all' || log.email_type === typeFilter;

        return matchesSearch && matchesStatus && matchesType;
    });

    // 匯出 Email 日誌
    const handleExportLogs = () => {
        const params = new URLSearchParams();
        if (statusFilter !== 'all') params.append('status', statusFilter);
        if (typeFilter !== 'all') params.append('type', typeFilter);
        if (searchTerm) params.append('search', searchTerm);

        window.open(`/admin/email-logs/export?${params.toString()}`, '_blank');
    };

    // 清理舊日誌
    const handleCleanupLogs = () => {
        if (confirm('確定要清理 30 天前的舊日誌嗎？此操作無法復原。')) {
            router.post(
                '/admin/email-logs/cleanup',
                { days: 30 },
                {
                    onSuccess: () => {
                        alert('舊日誌已清理完成');
                        router.reload();
                    },
                    onError: () => {
                        alert('清理失敗，請重試');
                    },
                },
            );
        }
    };

    return (
        <AdminLayout breadcrumbs={breadcrumbs} title="Email 管理" description="查看和管理系統發送的所有 Email 記錄">
            <Head title="Email 管理" />

            <div className="space-y-6 p-6">
                {/* 篩選和搜尋區域 */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <Filter className="h-5 w-5" />
                            篩選和搜尋
                        </CardTitle>
                        <CardDescription>使用下方工具來篩選和搜尋 Email 記錄</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
                            <div className="relative">
                                <Search className="absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 transform text-gray-400" />
                                <Input
                                    placeholder="搜尋收件人、主旨或系所..."
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                    className="pl-10"
                                />
                            </div>
                            <Select value={statusFilter} onValueChange={setStatusFilter}>
                                <SelectTrigger>
                                    <SelectValue placeholder="選擇狀態" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">所有狀態</SelectItem>
                                    <SelectItem value="sent">已發送</SelectItem>
                                    <SelectItem value="failed">發送失敗</SelectItem>
                                    <SelectItem value="pending">待發送</SelectItem>
                                </SelectContent>
                            </Select>
                            <Select value={typeFilter} onValueChange={setTypeFilter}>
                                <SelectTrigger>
                                    <SelectValue placeholder="選擇類型" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">所有類型</SelectItem>
                                    <SelectItem value="invitation">邀請信</SelectItem>
                                    <SelectItem value="reminder">提醒信</SelectItem>
                                    <SelectItem value="submitted">完成通知</SelectItem>
                                    <SelectItem value="declined">婉拒通知</SelectItem>
                                </SelectContent>
                            </Select>
                            <div className="flex gap-2">
                                <Button variant="outline" size="sm" className="flex flex-1 items-center gap-1" onClick={handleExportLogs}>
                                    <Download className="h-3 w-3" />
                                    <span className="text-xs">匯出記錄</span>
                                </Button>
                                <Button
                                    variant="outline"
                                    size="sm"
                                    className="flex flex-1 items-center gap-1 text-red-600 hover:text-red-700"
                                    onClick={handleCleanupLogs}
                                >
                                    <Trash2 className="h-3 w-3" />
                                    <span className="text-xs">清理舊記錄</span>
                                </Button>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* 統計資訊 */}
                <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center gap-2">
                                <Mail className="h-5 w-5 text-gray-500" />
                                <div>
                                    <div className="text-2xl font-bold">{emailLogs.total}</div>
                                    <p className="text-xs text-muted-foreground">總 Email 數</p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardContent className="p-6">
                            <div className="text-2xl font-bold text-green-600">{emailLogs.data.filter((log) => log.status === 'sent').length}</div>
                            <p className="text-xs text-muted-foreground">已發送</p>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardContent className="p-6">
                            <div className="text-2xl font-bold text-red-600">{emailLogs.data.filter((log) => log.status === 'failed').length}</div>
                            <p className="text-xs text-muted-foreground">發送失敗</p>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardContent className="p-6">
                            <div className="text-2xl font-bold text-yellow-600">
                                {emailLogs.data.filter((log) => log.status === 'pending').length}
                            </div>
                            <p className="text-xs text-muted-foreground">待發送</p>
                        </CardContent>
                    </Card>
                </div>

                {/* Email 記錄列表 */}
                <Card>
                    <CardHeader>
                        <CardTitle>Email 記錄</CardTitle>
                        <CardDescription>
                            顯示 {filteredLogs.length} / {emailLogs.data.length} 筆記錄
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            {filteredLogs.map((log) => (
                                <div key={log.id} className="flex items-center justify-between rounded-lg border p-4 hover:bg-gray-50">
                                    <div className="grid flex-1 grid-cols-1 gap-4 md:grid-cols-5">
                                        <div>
                                            <p className="text-sm text-gray-500">收件人</p>
                                            <p className="text-sm">{log.recipient_email}</p>
                                        </div>
                                        <div>
                                            <p className="text-sm text-gray-500">主旨</p>
                                            <p className="text-sm">{log.subject}</p>
                                        </div>
                                        <div>
                                            <p className="mt-1 text-sm text-gray-500">類型</p>
                                            {getTypeBadge(log.email_type)}
                                        </div>

                                        <div>
                                            <p className="text-sm font-medium">{new Date(log.sent_at || log.created_at).toLocaleString('zh-TW')}</p>
                                            <p className="text-sm text-gray-500">{log.recommendationLetter?.department_name}</p>
                                        </div>
                                        <div>
                                            {getStatusBadge(log.status)}
                                            {log.error_message && (
                                                <p className="mt-1 truncate text-xs text-red-500" title={log.error_message}>
                                                    {log.error_message}
                                                </p>
                                            )}
                                        </div>
                                    </div>
                                </div>
                            ))}

                            {filteredLogs.length === 0 && <div className="py-8 text-center text-gray-500">沒有找到符合條件的 Email 記錄</div>}
                        </div>
                    </CardContent>
                </Card>
            </div>
        </AdminLayout>
    );
}
