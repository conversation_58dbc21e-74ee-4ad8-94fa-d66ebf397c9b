<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\RecommendationController;
use App\Http\Controllers\QuestionnaireController;
use App\Http\Controllers\Auth\RecommenderAuthController;
use App\Http\Controllers\FileController;

/*
|--------------------------------------------------------------------------
| 推薦函相關路由
|--------------------------------------------------------------------------
|
| 這裡定義所有與推薦函系統相關的路由，包括申請人的推薦函管理、
| 推薦人的推薦函處理、問卷模板管理等功能
|
*/

// 申請人推薦函可用路由
Route::middleware(['auth', 'check.user.agreement', 'check.applicant.access'])->prefix('recommendations')->name('recommendations.')->group(function () {

  /** 建立推薦函 - 申請人建立新的推薦函請求 */
  Route::post('/', [RecommendationController::class, 'createRecommendation'])
    ->name('create');

  /** 發送提醒 - 申請人向推薦人發送提醒通知 */
  Route::post('/{id}/remind', [RecommendationController::class, 'sendReminder'])
    ->middleware('check.reminder.cooldown')
    ->name('remind');

  /** 撤回推薦函 - 申請人撤回推薦函請求 */
  Route::post('/{id}/withdraw', [RecommendationController::class, 'withdraw'])
    ->name('withdraw');
});



// 推薦人推薦函可用路由
Route::middleware(['auth', 'check.user.agreement', 'check.recommender.access'])->group(function () {

  // 推薦函處理

  /** 拒絕推薦邀請 - 推薦人拒絕推薦函邀請 */
  Route::post('/recommendations/{id}/decline', [RecommendationController::class, 'declineRecommendation'])
    ->name('recommendations.decline');

  /** 提交推薦函 - 推薦人提交完成的推薦函 */
  Route::post('/recommendations/{id}/submit', [RecommendationController::class, 'submitRecommendation'])
    ->name('recommendations.submit');

  // 問卷相關功能
  /** 取得問卷模板 - 根據推薦函 ID 取得對應的問卷模板 (API) */
  Route::get('/api/questionnaire/template/{recommendationId}', [QuestionnaireController::class, 'getTemplate'])
    ->name('questionnaire.template.api');

  // 推薦人個人功能
  /** 推薦人資料更新 - 更新推薦人個人資料 */
  Route::post('/recommender/profile/update', [RecommenderAuthController::class, 'updateProfile'])
    ->name('recommender.profile.update');

  /** 撤回推薦函 - 推薦人撤回已提交的推薦函 */
  Route::post('/recommendations/{id}/withdraw', [RecommendationController::class, 'withdrawRecommendation'])
    ->name('recommendation.withdraw');
});



// 問卷模板管理路由 (管理員功能)
Route::middleware(['auth', 'check.user.agreement', 'role:admin'])->prefix('admin/questionnaire')->name('admin.questionnaire.')->group(function () {

  /** 問卷模板列表 - 管理員查看所有問卷模板 */
  Route::get('/templates', [QuestionnaireController::class, 'index'])
    ->name('templates.index');

  /** 上傳 CSV 模板 - 管理員透過 CSV 檔案建立問卷模板 */
  Route::post('/upload-csv', [QuestionnaireController::class, 'uploadCsvTemplate'])
    ->name('upload-csv');

  /** 儲存問卷模板 - 管理員建立或更新問卷模板 */
  Route::post('/save-template', [QuestionnaireController::class, 'saveTemplate'])
    ->name('save-template');

  /** 刪除問卷模板 - 管理員刪除指定的問卷模板 */
  Route::delete('/templates/{id}', [QuestionnaireController::class, 'destroy'])
    ->name('templates.destroy');
});

// 檔案訪問路由 (除了考生)
// todo 新增檔案訪問中間件(推薦人只能檢視自己的推薦函，管理員不受限制)，並同步調整呼叫端
Route::middleware(['auth', 'check.user.agreement', 'role:admin,recommender'])->prefix('files')->name('files.')->group(function () {

  /** PDF 檔案預覽 - 在瀏覽器中預覽 PDF 檔案 */
  Route::get('/preview-pdf', [FileController::class, 'previewPdf'])
    ->name('preview-pdf');
});
