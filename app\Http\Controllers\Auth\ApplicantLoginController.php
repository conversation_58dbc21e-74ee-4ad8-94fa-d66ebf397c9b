<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Services\AuthenticationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;


/**
 * 申請人登入控制器
 *
 * 處理申請人透過外部系統 token 的跳轉登入邏輯
 * 使用統一的 AuthenticationService 進行認證處理
 */
class ApplicantLoginController extends Controller
{
  protected AuthenticationService $authService;

  public function __construct(AuthenticationService $authService)
  {
    $this->authService = $authService;
  }

  /**
   * 處理招生系統考生登入
   *
   * @param Request $request
   * @return \Illuminate\Http\RedirectResponse|\Illuminate\Http\Response
   */
  public function handleExternalAuth(Request $request)
  {
    $token = $request->query('token');

    if (!$token) {
      Log::warning('第三方登入失敗: 缺少 token 參數', [
        'ip' => $request->ip(),
        'user_agent' => $request->userAgent(),
      ]);

      return $this->showAuthFailure('登入失敗，請確認您的登入連結是否正確');
    }

    try {
      // 將 token 送回 API 系統進行解碼和驗證
      $result = $this->authService->verifyTokenWithApi($token, $request->ip());

      if ($result['success']) {
        return redirect()->route('dashboard')->with('success', '登入成功！歡迎使用推薦函管理系統');
      } else {
        return $this->showAuthFailure($result['message']);
      }
    } catch (\Exception $e) {
      Log::error('第三方登入失敗: 未知錯誤', [
        'token' => substr($token, 0, 20) . '...',
        'error' => $e->getMessage(),
        'trace' => $e->getTraceAsString(),
      ]);

      return $this->showAuthFailure('登入失敗，請稍後再試或聯繫系統管理員');
    }
  }

  /**
   * 顯示認證失敗頁面
   */
  private function showAuthFailure(string $message)
  {
    return inertia('auth/auth-failure', [
      'message' => $message,
      'type' => 'applicant'
    ]);
  }

  /**
   * 處理申請人登出
   * 使用統一認證服務處理登出邏輯
   */
  public function logout()
  {
    $this->authService->logout('applicant');
    return redirect()->route('home')->with('success', '您已成功登出。');
  }
}
