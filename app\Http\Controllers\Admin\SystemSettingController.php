<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\SystemLog;
use App\Models\SystemSetting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Inertia\Inertia;

/**
 * 系統設定控制器
 * 
 * 管理系統的各種設定參數
 */
class SystemSettingController extends Controller
{
    /**
     * 顯示系統設定頁面
     *
     * @return \Inertia\Response
     */
    public function index()
    {
        try {
            $settings = [
                'timing' => SystemSetting::getByCategory(SystemSetting::CATEGORY_TIMING),
                'general' => SystemSetting::getByCategory(SystemSetting::CATEGORY_GENERAL),
                'security' => SystemSetting::getByCategory(SystemSetting::CATEGORY_SECURITY),
                'email' => SystemSetting::getByCategory(SystemSetting::CATEGORY_EMAIL),
            ];

            // 系統狀態資訊
            $systemStatus = [
                'is_system_open' => SystemSetting::isSystemOpen(),
                'is_recruitment_period' => SystemSetting::isInRecruitmentPeriod(),
                'is_questionnaire_locked' => SystemSetting::isQuestionnaireEditLocked(),
                'reminder_cooldown_hours' => SystemSetting::getReminderCooldownHours(),
                'auto_timeout_days' => SystemSetting::getAutoTimeoutDays(),
                'recruitment_period' => SystemSetting::getRecruitmentPeriod(),
            ];

            SystemLog::logOperation(
                SystemLog::ACTION_VIEW,
                '查看系統設定頁面',
                ['page' => 'system_settings']
            );

            return Inertia::render('admin/system-settings', [
                'settings' => $settings,
                'system_status' => $systemStatus,
            ]);
        } catch (\Exception $e) {
            SystemLog::logError(
                SystemLog::ACTION_VIEW,
                '查看系統設定頁面失敗',
                $e
            );

            return back()->withErrors([
                'system' => '載入系統設定失敗，請稍後再試'
            ]);
        }
    }

    /**
     * 更新系統設定
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request)
    {
        try {
            // 驗證前端表單資料
            $validator = Validator::make($request->all(), [
                'system_open_time' => 'nullable|date',
                'system_close_time' => 'nullable|date',
                'recruitment_period_start' => 'nullable|date',
                'recruitment_period_end' => 'nullable|date|after:recruitment_period_start',
                'reminder_cooldown' => 'required|integer|min:1|max:168',
                'auto_timeout' => 'required|integer|min:1|max:30',
                'questionnaire_locked' => 'required|boolean',
            ]);

            if ($validator->fails()) {
                return back()->withErrors($validator->errors());
            }

            $updatedSettings = [];
            $user = Auth::user();

            // 定義設定映射
            $settingsMap = [
                'system_open_time' => [
                    'key' => SystemSetting::SYSTEM_OPEN_TIME,
                    'type' => SystemSetting::TYPE_DATETIME,
                    'category' => SystemSetting::CATEGORY_TIMING,
                    'description' => '系統開放時間',
                ],
                'system_close_time' => [
                    'key' => SystemSetting::SYSTEM_CLOSE_TIME,
                    'type' => SystemSetting::TYPE_DATETIME,
                    'category' => SystemSetting::CATEGORY_TIMING,
                    'description' => '系統關閉時間',
                ],
                'recruitment_period_start' => [
                    'key' => SystemSetting::RECRUITMENT_PERIOD_START,
                    'type' => SystemSetting::TYPE_DATETIME,
                    'category' => SystemSetting::CATEGORY_TIMING,
                    'description' => '招生期間開始時間',
                ],
                'recruitment_period_end' => [
                    'key' => SystemSetting::RECRUITMENT_PERIOD_END,
                    'type' => SystemSetting::TYPE_DATETIME,
                    'category' => SystemSetting::CATEGORY_TIMING,
                    'description' => '招生期間結束時間',
                ],
                'reminder_cooldown' => [
                    'key' => SystemSetting::REMINDER_COOLDOWN_HOURS,
                    'type' => SystemSetting::TYPE_INTEGER,
                    'category' => SystemSetting::CATEGORY_TIMING,
                    'description' => '提醒郵件冷卻時間（小時）',
                ],
                'auto_timeout' => [
                    'key' => SystemSetting::AUTO_TIMEOUT_DAYS,
                    'type' => SystemSetting::TYPE_INTEGER,
                    'category' => SystemSetting::CATEGORY_TIMING,
                    'description' => '自動超時天數',
                ],
                'questionnaire_locked' => [
                    'key' => SystemSetting::QUESTIONNAIRE_EDIT_LOCKED,
                    'type' => SystemSetting::TYPE_BOOLEAN,
                    'category' => SystemSetting::CATEGORY_GENERAL,
                    'description' => '問卷編輯鎖定狀態',
                ],
            ];

            // 更新每個設定
            foreach ($settingsMap as $formField => $settingConfig) {
                if ($request->has($formField)) {
                    $value = $request->input($formField);

                    // 處理空值
                    if ($value === null || $value === '') {
                        continue;
                    }

                    $success = SystemSetting::set(
                        $settingConfig['key'],
                        $value,
                        $settingConfig['type'],
                        $settingConfig['category'],
                        $settingConfig['description']
                    );

                    if ($success) {
                        $updatedSettings[] = $settingConfig['key'];
                    }
                }
            }

            // 記錄操作日誌
            SystemLog::logOperation(
                SystemLog::ACTION_SYSTEM_SETTING,
                "管理員 {$user->name} 更新了系統設定",
                [
                    'updated_settings' => $updatedSettings,
                    'total_updated' => count($updatedSettings),
                ]
            );

            return back()->with('success', '系統設定已成功更新');
        } catch (\Exception $e) {
            SystemLog::logError(
                SystemLog::ACTION_SYSTEM_SETTING,
                '更新系統設定失敗',
                $e,
                ['request_data' => $request->all()]
            );

            return back()->withErrors([
                'system' => '更新系統設定失敗，請稍後再試'
            ]);
        }
    }

    /**
     * 更新單一設定
     *
     * @param Request $request
     * @param string $key
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateSingle(Request $request, string $key)
    {
        try {
            $validator = Validator::make($request->all(), [
                'value' => 'required',
                'type' => 'required|string|in:string,json,boolean,datetime,integer',
                'category' => 'required|string',
                'description' => 'nullable|string',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => '驗證失敗',
                    'errors' => $validator->errors()
                ], 422);
            }

            $success = SystemSetting::set(
                $key,
                $request->input('value'),
                $request->input('type'),
                $request->input('category'),
                $request->input('description')
            );

            if (!$success) {
                return response()->json([
                    'success' => false,
                    'message' => '設定更新失敗'
                ], 500);
            }

            $user = Auth::user();
            SystemLog::logOperation(
                SystemLog::ACTION_SYSTEM_SETTING,
                "管理員 {$user->name} 更新了設定: {$key}",
                [
                    'setting_key' => $key,
                    'new_value' => $request->input('value'),
                    'type' => $request->input('type'),
                ]
            );

            return response()->json([
                'success' => true,
                'message' => '設定已成功更新'
            ]);
        } catch (\Exception $e) {
            SystemLog::logError(
                SystemLog::ACTION_SYSTEM_SETTING,
                "更新單一設定失敗: {$key}",
                $e,
                ['request_data' => $request->all()]
            );

            return response()->json([
                'success' => false,
                'message' => '設定更新失敗，請稍後再試'
            ], 500);
        }
    }

    /**
     * 重置為預設設定
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function resetDefaults()
    {
        try {
            SystemSetting::initializeDefaults();

            $user = Auth::user();
            SystemLog::logOperation(
                SystemLog::ACTION_SYSTEM_SETTING,
                "管理員 {$user->name} 重置了系統設定為預設值",
                ['action' => 'reset_defaults']
            );

            return back()->with('success', '系統設定已重置為預設值');
        } catch (\Exception $e) {
            SystemLog::logError(
                SystemLog::ACTION_SYSTEM_SETTING,
                '重置系統設定失敗',
                $e
            );

            return back()->withErrors([
                'system' => '重置系統設定失敗，請稍後再試'
            ]);
        }
    }

    /**
     * 取得系統狀態
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getSystemStatus()
    {
        try {
            $status = [
                'is_system_open' => SystemSetting::isSystemOpen(),
                'is_recruitment_period' => SystemSetting::isInRecruitmentPeriod(),
                'is_questionnaire_locked' => SystemSetting::isQuestionnaireEditLocked(),
                'reminder_cooldown_hours' => SystemSetting::getReminderCooldownHours(),
                'auto_timeout_days' => SystemSetting::getAutoTimeoutDays(),
                'recruitment_period' => SystemSetting::getRecruitmentPeriod(),
                'current_time' => now()->toDateTimeString(),
            ];

            return response()->json([
                'success' => true,
                'data' => $status
            ]);
        } catch (\Exception $e) {
            SystemLog::logError(
                SystemLog::ACTION_VIEW,
                '取得系統狀態失敗',
                $e
            );

            return response()->json([
                'success' => false,
                'message' => '取得系統狀態失敗'
            ], 500);
        }
    }

    /**
     * 切換問卷編輯鎖定狀態
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function toggleQuestionnaireLock(Request $request)
    {
        try {
            // 如果請求中有 locked 參數，使用該值；否則切換當前狀態
            if ($request->has('locked')) {
                $newStatus = $request->boolean('locked');
            } else {
                $currentStatus = SystemSetting::isQuestionnaireEditLocked();
                $newStatus = !$currentStatus;
            }

            $success = SystemSetting::set(
                SystemSetting::QUESTIONNAIRE_EDIT_LOCKED,
                $newStatus,
                SystemSetting::TYPE_BOOLEAN,
                SystemSetting::CATEGORY_TIMING,
                '問卷編輯鎖定狀態'
            );

            if (!$success) {
                return response()->json([
                    'success' => false,
                    'message' => '切換鎖定狀態失敗'
                ], 500);
            }

            $user = Auth::user();
            $action = $newStatus ? '鎖定' : '解鎖';
            SystemLog::logOperation(
                SystemLog::ACTION_SYSTEM_SETTING,
                "管理員 {$user->name} {$action}了問卷編輯功能",
                [
                    'previous_status' => $currentStatus,
                    'new_status' => $newStatus,
                ]
            );

            return response()->json([
                'success' => true,
                'message' => "問卷編輯功能已{$action}",
                'is_locked' => $newStatus
            ]);
        } catch (\Exception $e) {
            SystemLog::logError(
                SystemLog::ACTION_SYSTEM_SETTING,
                '切換問卷編輯鎖定狀態失敗',
                $e
            );

            return response()->json([
                'success' => false,
                'message' => '切換鎖定狀態失敗，請稍後再試'
            ], 500);
        }
    }
}
