import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Edit, Eye, FileText, Plus, Trash2 } from 'lucide-react';
import { useEffect, useState } from 'react';

interface Question {
    id: string;
    question: string;
    type: 'text' | 'textarea' | 'select' | 'radio' | 'checkbox';
    required: boolean;
    options?: string[];
    max_length?: number;
    placeholder?: string;
}

interface QuestionnaireTemplate {
    id: number;
    department_name: string;
    program_type: string;
    template_name: string;
    questions: Question[];
    is_active: boolean;
    created_at: string;
    updated_at: string;
}

export default function QuestionnaireTemplateManager() {
    const [templates, setTemplates] = useState<QuestionnaireTemplate[]>([]);
    const [isLoading, setIsLoading] = useState(true);
    const [showCreateForm, setShowCreateForm] = useState(false);
    const [editingTemplate, setEditingTemplate] = useState<QuestionnaireTemplate | null>(null);
    const [previewTemplate, setPreviewTemplate] = useState<QuestionnaireTemplate | null>(null);

    // Form state
    const [formData, setFormData] = useState({
        department_name: '',
        program_type: 'master',
        template_name: '',
        questions: [] as Question[],
    });

    useEffect(() => {
        loadTemplates();
    }, []);

    const loadTemplates = async () => {
        try {
            setIsLoading(true);
            const response = await fetch('admin/questionnaire/templates');
            const data = await response.json();

            // Parse questions JSON if it's a string
            const processedTemplates = (data.templates || []).map((template: any) => ({
                ...template,
                questions: typeof template.questions === 'string' ? JSON.parse(template.questions) : template.questions,
            }));

            setTemplates(processedTemplates);
        } catch (error) {
            console.error('Error loading templates:', error);
        } finally {
            setIsLoading(false);
        }
    };

    const handleCreateTemplate = () => {
        setFormData({
            department_name: '',
            program_type: 'master',
            template_name: '',
            questions: [],
        });
        setEditingTemplate(null);
        setShowCreateForm(true);
    };

    const handleEditTemplate = (template: QuestionnaireTemplate) => {
        setFormData({
            department_name: template.department_name,
            program_type: template.program_type,
            template_name: template.template_name,
            questions: template.questions,
        });
        setEditingTemplate(template);
        setShowCreateForm(true);
    };

    const handleSaveTemplate = async () => {
        try {
            const response = await fetch('admin/questionnaire/save-template', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                },
                body: JSON.stringify(formData),
            });

            if (response.ok) {
                await loadTemplates();
                setShowCreateForm(false);
                setEditingTemplate(null);
            } else {
                console.error('Error saving template');
            }
        } catch (error) {
            console.error('Error saving template:', error);
        }
    };

    const handleDeleteTemplate = async (templateId: number) => {
        if (!confirm('確定要刪除此問卷模板嗎？')) return;

        try {
            const response = await fetch(`admin/questionnaire/templates/${templateId}`, {
                method: 'DELETE',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                },
            });

            if (response.ok) {
                await loadTemplates();
            } else {
                console.error('Error deleting template');
            }
        } catch (error) {
            console.error('Error deleting template:', error);
        }
    };

    const addQuestion = () => {
        const newQuestion: Question = {
            id: `q_${Date.now()}`,
            question: '',
            type: 'text',
            required: false,
            placeholder: '',
        };
        setFormData((prev) => ({
            ...prev,
            questions: [...prev.questions, newQuestion],
        }));
    };

    const updateQuestion = (index: number, field: keyof Question, value: any) => {
        setFormData((prev) => ({
            ...prev,
            questions: prev.questions.map((q, i) => (i === index ? { ...q, [field]: value } : q)),
        }));
    };

    const removeQuestion = (index: number) => {
        setFormData((prev) => ({
            ...prev,
            questions: prev.questions.filter((_, i) => i !== index),
        }));
    };

    const getTypeLabel = (type: string) => {
        const labels = {
            text: '單行文字',
            textarea: '多行文字',
            select: '下拉選單',
            radio: '單選',
            checkbox: '多選',
        };
        return labels[type as keyof typeof labels] || type;
    };

    if (isLoading) {
        return (
            <div className="flex items-center justify-center p-8">
                <div className="text-center">
                    <div className="mx-auto mb-4 h-8 w-8 animate-spin rounded-full border-b-2 border-blue-600"></div>
                    <p className="text-gray-600">載入問卷模板中...</p>
                </div>
            </div>
        );
    }

    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="flex items-center justify-between">
                <div>
                    <h2 className="text-2xl font-bold text-gray-900">問卷模板管理</h2>
                    <p className="text-gray-600">管理推薦函問卷模板</p>
                </div>
                <Button onClick={handleCreateTemplate} className="bg-blue-600 hover:bg-blue-700">
                    <Plus className="mr-2 h-4 w-4" />
                    新增模板
                </Button>
            </div>

            {/* Templates List */}
            <div className="grid gap-4">
                {templates.length === 0 ? (
                    <Card>
                        <CardContent className="p-8 text-center">
                            <FileText className="mx-auto mb-4 h-12 w-12 text-gray-400" />
                            <h3 className="mb-2 text-lg font-semibold text-gray-900">尚無問卷模板</h3>
                            <p className="mb-4 text-gray-600">開始創建您的第一個問卷模板</p>
                            <Button onClick={handleCreateTemplate} className="bg-blue-600 hover:bg-blue-700">
                                <Plus className="mr-2 h-4 w-4" />
                                新增模板
                            </Button>
                        </CardContent>
                    </Card>
                ) : (
                    templates.map((template) => (
                        <Card key={template.id} className="transition-shadow hover:shadow-md">
                            <CardHeader>
                                <div className="flex items-start justify-between">
                                    <div>
                                        <CardTitle className="text-lg">{template.template_name}</CardTitle>
                                        <CardDescription>
                                            {template.department_name} - {template.program_type}
                                        </CardDescription>
                                    </div>
                                    <div className="flex items-center space-x-2">
                                        {template.is_active && (
                                            <Badge variant="secondary" className="bg-green-100 text-green-800">
                                                啟用中
                                            </Badge>
                                        )}
                                        <div className="flex space-x-1">
                                            <Button size="sm" variant="outline" onClick={() => setPreviewTemplate(template)}>
                                                <Eye className="h-4 w-4" />
                                            </Button>
                                            <Button size="sm" variant="outline" onClick={() => handleEditTemplate(template)}>
                                                <Edit className="h-4 w-4" />
                                            </Button>
                                            <Button
                                                size="sm"
                                                variant="outline"
                                                onClick={() => handleDeleteTemplate(template.id)}
                                                className="text-red-600 hover:text-red-700"
                                            >
                                                <Trash2 className="h-4 w-4" />
                                            </Button>
                                        </div>
                                    </div>
                                </div>
                            </CardHeader>
                            <CardContent>
                                <div className="text-sm text-gray-600">
                                    <p>問題數量：{template.questions.length}</p>
                                    <p>建立時間：{new Date(template.created_at).toLocaleDateString()}</p>
                                    <p>更新時間：{new Date(template.updated_at).toLocaleDateString()}</p>
                                </div>
                            </CardContent>
                        </Card>
                    ))
                )}
            </div>

            {/* Create/Edit Form Modal */}
            {showCreateForm && (
                <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 p-4">
                    <div className="max-h-[90vh] w-full max-w-4xl overflow-y-auto rounded-lg bg-white shadow-xl">
                        <div className="border-b p-6">
                            <h3 className="text-lg font-semibold">{editingTemplate ? '編輯問卷模板' : '新增問卷模板'}</h3>
                        </div>

                        <div className="space-y-6 p-6">
                            {/* Basic Info */}
                            <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                                <div className="space-y-2">
                                    <Label htmlFor="department_name">科系名稱</Label>
                                    <Input
                                        id="department_name"
                                        value={formData.department_name}
                                        onChange={(e) => setFormData((prev) => ({ ...prev, department_name: e.target.value }))}
                                        placeholder="例：資訊工程學系"
                                    />
                                </div>
                                <div className="space-y-2">
                                    <Label htmlFor="program_type">申請類別</Label>
                                    <Select
                                        value={formData.program_type}
                                        onValueChange={(value) => setFormData((prev) => ({ ...prev, program_type: value }))}
                                    >
                                        <SelectTrigger>
                                            <SelectValue />
                                        </SelectTrigger>
                                        <SelectContent></SelectContent>
                                    </Select>
                                </div>
                                <div className="space-y-2">
                                    <Label htmlFor="template_name">模板名稱</Label>
                                    <Input
                                        id="template_name"
                                        value={formData.template_name}
                                        onChange={(e) => setFormData((prev) => ({ ...prev, template_name: e.target.value }))}
                                        placeholder="例：標準推薦函問卷"
                                    />
                                </div>
                            </div>

                            {/* Questions */}
                            <div className="space-y-4">
                                <div className="flex items-center justify-between">
                                    <h4 className="text-lg font-semibold">問題設定</h4>
                                    <Button onClick={addQuestion} size="sm" variant="outline">
                                        <Plus className="mr-2 h-4 w-4" />
                                        新增問題
                                    </Button>
                                </div>

                                {formData.questions.map((question, index) => (
                                    <Card key={question.id} className="p-4">
                                        <div className="space-y-4">
                                            <div className="flex items-start justify-between">
                                                <h5 className="font-medium">問題 {index + 1}</h5>
                                                <Button size="sm" variant="outline" onClick={() => removeQuestion(index)} className="text-red-600">
                                                    <Trash2 className="h-4 w-4" />
                                                </Button>
                                            </div>

                                            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                                <div className="space-y-2">
                                                    <Label>問題內容</Label>
                                                    <Textarea
                                                        value={question.question}
                                                        onChange={(e) => updateQuestion(index, 'question', e.target.value)}
                                                        placeholder="輸入問題內容..."
                                                        rows={2}
                                                    />
                                                </div>
                                                <div className="space-y-2">
                                                    <Label>問題類型</Label>
                                                    <Select value={question.type} onValueChange={(value) => updateQuestion(index, 'type', value)}>
                                                        <SelectTrigger>
                                                            <SelectValue />
                                                        </SelectTrigger>
                                                        <SelectContent>
                                                            <SelectItem value="text">單行文字</SelectItem>
                                                            <SelectItem value="textarea">多行文字</SelectItem>
                                                            <SelectItem value="select">下拉選單</SelectItem>
                                                            <SelectItem value="radio">單選</SelectItem>
                                                            <SelectItem value="checkbox">多選</SelectItem>
                                                        </SelectContent>
                                                    </Select>
                                                </div>
                                            </div>

                                            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                                <div className="space-y-2">
                                                    <Label>提示文字</Label>
                                                    <Input
                                                        value={question.placeholder || ''}
                                                        onChange={(e) => updateQuestion(index, 'placeholder', e.target.value)}
                                                        placeholder="輸入提示文字..."
                                                    />
                                                </div>
                                                <div className="space-y-2">
                                                    <Label>字數限制</Label>
                                                    <Input
                                                        type="number"
                                                        value={question.max_length || ''}
                                                        onChange={(e) => updateQuestion(index, 'max_length', parseInt(e.target.value) || undefined)}
                                                        placeholder="例：500"
                                                    />
                                                </div>
                                            </div>

                                            {(question.type === 'select' || question.type === 'radio' || question.type === 'checkbox') && (
                                                <div className="space-y-2">
                                                    <Label>選項（每行一個）</Label>
                                                    <Textarea
                                                        value={(question.options || []).join('\n')}
                                                        onChange={(e) =>
                                                            updateQuestion(
                                                                index,
                                                                'options',
                                                                e.target.value.split('\n').filter((o) => o.trim()),
                                                            )
                                                        }
                                                        placeholder="選項1&#10;選項2&#10;選項3"
                                                        rows={3}
                                                    />
                                                </div>
                                            )}

                                            <div className="flex items-center space-x-2">
                                                <input
                                                    type="checkbox"
                                                    id={`required_${index}`}
                                                    checked={question.required}
                                                    onChange={(e) => updateQuestion(index, 'required', e.target.checked)}
                                                    className="rounded border-gray-300"
                                                />
                                                <Label htmlFor={`required_${index}`}>必填問題</Label>
                                            </div>
                                        </div>
                                    </Card>
                                ))}

                                {formData.questions.length === 0 && (
                                    <div className="py-8 text-center text-gray-500">
                                        <FileText className="mx-auto mb-2 h-8 w-8 text-gray-400" />
                                        <p>尚未新增任何問題</p>
                                        <p className="text-sm">點擊「新增問題」開始建立問卷</p>
                                    </div>
                                )}
                            </div>
                        </div>

                        <div className="flex justify-end space-x-3 border-t p-6">
                            <Button
                                variant="outline"
                                onClick={() => {
                                    setShowCreateForm(false);
                                    setEditingTemplate(null);
                                }}
                            >
                                取消
                            </Button>
                            <Button
                                onClick={handleSaveTemplate}
                                className="bg-blue-600 hover:bg-blue-700"
                                disabled={!formData.template_name || !formData.department_name || formData.questions.length === 0}
                            >
                                {editingTemplate ? '更新模板' : '建立模板'}
                            </Button>
                        </div>
                    </div>
                </div>
            )}

            {/* Preview Modal */}
            {previewTemplate && (
                <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 p-4">
                    <div className="max-h-[90vh] w-full max-w-3xl overflow-y-auto rounded-lg bg-white shadow-xl">
                        <div className="border-b p-6">
                            <div className="flex items-center justify-between">
                                <h3 className="text-lg font-semibold">預覽問卷模板</h3>
                                <Button variant="outline" onClick={() => setPreviewTemplate(null)}>
                                    關閉
                                </Button>
                            </div>
                        </div>

                        <div className="p-6">
                            <div className="mb-6">
                                <h4 className="text-xl font-bold">{previewTemplate.template_name}</h4>
                                <p className="text-gray-600">
                                    {previewTemplate.department_name} - {previewTemplate.program_type}
                                </p>
                            </div>

                            <div className="space-y-6">
                                {previewTemplate.questions.map((question, index) => (
                                    <div key={question.id} className="rounded-lg border p-4">
                                        <div className="mb-2 flex items-start justify-between">
                                            <h5 className="font-medium">
                                                {index + 1}. {question.question}
                                                {question.required && <span className="ml-1 text-red-500">*</span>}
                                            </h5>
                                            <Badge variant="outline">{getTypeLabel(question.type)}</Badge>
                                        </div>

                                        {question.placeholder && <p className="mb-2 text-sm text-gray-500">提示：{question.placeholder}</p>}

                                        {question.max_length && <p className="mb-2 text-sm text-gray-500">字數限制：{question.max_length}</p>}

                                        {question.options && question.options.length > 0 && (
                                            <div className="mt-2">
                                                <p className="mb-1 text-sm text-gray-600">選項：</p>
                                                <ul className="list-inside list-disc text-sm text-gray-600">
                                                    {question.options.map((option, optIndex) => (
                                                        <li key={optIndex}>{option}</li>
                                                    ))}
                                                </ul>
                                            </div>
                                        )}
                                    </div>
                                ))}
                            </div>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
}
