import AdminLayout from '@/layouts/admin-layout';
import { Head } from '@inertiajs/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Search, Filter, Plus, Edit, Eye, Trash2, Lock, Unlock } from 'lucide-react';
import { useState } from 'react';
import { router } from '@inertiajs/react';

interface QuestionnaireTemplate {
    id: number;
    department_name: string;
    program_type: string;
    template_name: string;
    description: string;
    questions: any;
    is_active: boolean;
    sort_order: number;
    created_at: string;
    updated_at: string;
}

interface QuestionnaireTemplatesProps {
    templates: QuestionnaireTemplate[];
    isLocked: boolean;
}

export default function QuestionnaireTemplates({ templates, isLocked }: QuestionnaireTemplatesProps) {
    const [searchTerm, setSearchTerm] = useState('');
    const [statusFilter, setStatusFilter] = useState('all');
    const [departmentFilter, setDepartmentFilter] = useState('all');

    // 麵包屑導航
    const breadcrumbs = [
        { title: '儀表板', href: '/dashboard' },
        { title: '問卷模板', href: '/admin/questionnaire-templates' },
    ];

    // 狀態徽章
    const getStatusBadge = (isActive: boolean) => {
        return isActive ? (
            <Badge variant="secondary" className="bg-green-100 text-green-800">
                啟用中
            </Badge>
        ) : (
            <Badge variant="secondary" className="bg-gray-100 text-gray-800">
                已停用
            </Badge>
        );
    };

    // 過濾模板
    const filteredTemplates = templates.filter((template) => {
        const matchesSearch =
            template.template_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            template.department_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            template.program_type.toLowerCase().includes(searchTerm.toLowerCase()) ||
            template.description.toLowerCase().includes(searchTerm.toLowerCase());

        const matchesStatus =
            statusFilter === 'all' || (statusFilter === 'active' && template.is_active) || (statusFilter === 'inactive' && !template.is_active);

        const matchesDepartment = departmentFilter === 'all' || template.department_name === departmentFilter;

        return matchesSearch && matchesStatus && matchesDepartment;
    });

    // 獲取所有系所
    const departments = Array.from(new Set(templates.map((template) => template.department_name)));

    // 計算問題數量
    const getQuestionCount = (questions: any) => {
        if (!questions) return 0;
        try {
            const parsed = typeof questions === 'string' ? JSON.parse(questions) : questions;
            return Array.isArray(parsed) ? parsed.length : 0;
        } catch {
            return 0;
        }
    };

    return (
        <AdminLayout breadcrumbs={breadcrumbs} title="問卷模板管理" description="管理推薦函問卷模板，設定不同系所和學程的問卷內容">
            <Head title="問卷模板管理" />

            <div className="space-y-6 p-6">
                {/* 鎖定狀態提示 */}
                {isLocked && (
                    <Card className="border-orange-200 bg-orange-50">
                        <CardContent>
                            <div className="flex items-center gap-3">
                                <Lock className="h-5 w-5 text-orange-600" />
                                <div>
                                    <p className="text-sm font-medium text-orange-800">問卷模板編輯已鎖定</p>
                                    <p className="text-sm text-orange-700">目前無法新增、編輯或刪除問卷模板。請聯繫系統管理員解鎖。</p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                )}

                {/* 操作區域 */}
                <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                        {isLocked ? <Lock className="h-5 w-5 text-orange-500" /> : <Unlock className="h-5 w-5 text-green-500" />}
                        <span className="text-sm text-gray-600">編輯狀態：{isLocked ? '已鎖定' : '可編輯'}</span>
                    </div>
                    <Button
                        size="sm"
                        className="flex items-center gap-1"
                        disabled={isLocked}
                        onClick={() => router.get('/admin/questionnaire-templates/create')}
                    >
                        <Plus className="h-3 w-3" />
                        <span className="text-xs">新增模板</span>
                    </Button>
                </div>

                {/* 篩選和搜尋區域 */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <Filter className="h-5 w-5" />
                            篩選和搜尋
                        </CardTitle>
                        <CardDescription>使用下方工具來篩選和搜尋問卷模板</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
                            <div className="relative">
                                <Search className="absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 transform text-gray-400" />
                                <Input
                                    placeholder="搜尋模板名稱、系所或描述..."
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                    className="pl-10"
                                />
                            </div>
                            <Select value={statusFilter} onValueChange={setStatusFilter}>
                                <SelectTrigger>
                                    <SelectValue placeholder="選擇狀態" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">所有狀態</SelectItem>
                                    <SelectItem value="active">啟用中</SelectItem>
                                    <SelectItem value="inactive">已停用</SelectItem>
                                </SelectContent>
                            </Select>
                            <Select value={departmentFilter} onValueChange={setDepartmentFilter}>
                                <SelectTrigger>
                                    <SelectValue placeholder="選擇系所" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">所有系所</SelectItem>
                                    {departments.map((dept) => (
                                        <SelectItem key={dept} value={dept}>
                                            {dept}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                            <div className="flex gap-2">
                                <Button variant="outline" size="sm" className="flex-1">
                                    <span className="text-xs">匯出模板</span>
                                </Button>
                                <Button variant="outline" size="sm" className="flex-1">
                                    <span className="text-xs">匯入模板</span>
                                </Button>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* 統計資訊 */}
                <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
                    <Card>
                        <CardContent className="p-6">
                            <div className="text-2xl font-bold">{templates.length}</div>
                            <p className="text-xs text-muted-foreground">總模板數</p>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardContent className="p-6">
                            <div className="text-2xl font-bold text-green-600">{templates.filter((t) => t.is_active).length}</div>
                            <p className="text-xs text-muted-foreground">啟用中</p>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardContent className="p-6">
                            <div className="text-2xl font-bold text-gray-600">{templates.filter((t) => !t.is_active).length}</div>
                            <p className="text-xs text-muted-foreground">已停用</p>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardContent className="p-6">
                            <div className="text-2xl font-bold text-blue-600">{departments.length}</div>
                            <p className="text-xs text-muted-foreground">系所數量</p>
                        </CardContent>
                    </Card>
                </div>

                {/* 模板列表 */}
                <Card>
                    <CardHeader>
                        <CardTitle>問卷模板列表</CardTitle>
                        <CardDescription>
                            顯示 {filteredTemplates.length} / {templates.length} 個模板
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            {filteredTemplates.map((template) => (
                                <div key={template.id} className="flex items-center justify-between rounded-lg border p-4 hover:bg-gray-50">
                                    <div className="grid flex-1 grid-cols-1 gap-4 md:grid-cols-4">
                                        <div>
                                            <p className="font-medium">{template.template_name}</p>
                                            <p className="text-sm text-gray-500">{template.description}</p>
                                        </div>
                                        <div>
                                            <p className="font-medium">{template.department_name}</p>
                                            <p className="text-sm text-gray-500">{template.program_type}</p>
                                        </div>
                                        <div>
                                            {getStatusBadge(template.is_active)}
                                            <p className="mt-1 text-sm text-gray-500">{getQuestionCount(template.questions)} 個問題</p>
                                        </div>
                                        <div>
                                            <p className="text-sm text-gray-500">
                                                建立於 {new Date(template.created_at).toLocaleDateString('zh-TW')}
                                            </p>
                                            <p className="text-sm text-gray-500">排序：{template.sort_order}</p>
                                        </div>
                                    </div>
                                    <div className="flex gap-2">
                                        <Button variant="outline" size="sm">
                                            <Eye className="h-4 w-4" />
                                        </Button>
                                        <Button variant="outline" size="sm" disabled={isLocked}>
                                            <Edit className="h-4 w-4" />
                                        </Button>
                                        <Button variant="outline" size="sm" disabled={isLocked} className="text-red-600 hover:text-red-700">
                                            <Trash2 className="h-4 w-4" />
                                        </Button>
                                    </div>
                                </div>
                            ))}

                            {filteredTemplates.length === 0 && <div className="py-8 text-center text-gray-500">沒有找到符合條件的問卷模板</div>}
                        </div>
                    </CardContent>
                </Card>
            </div>
        </AdminLayout>
    );
}
