<?php

namespace App\Http\Controllers;

use App\Models\UserAgreement;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;
use Inertia\Response;

class UserAgreementController extends Controller
{
    /**
     * Show the user agreement page.
     */
    public function show(): Response
    {
        $user = Auth::user();

        return Inertia::render('user-agreement', [
            'user' => [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'phone' =>  $user->phone,
            ],
            'userRole' => $user->role,
        ]);
    }

    /**
     * Store the user agreement.
     */
    public function store(Request $request)
    {
        $request->validate([
            'agree' => 'required|boolean',
        ]);

        $user = Auth::user();

        if (!$request->agree) {
            return back()->withErrors([
                'agree' => '您必須同意個人資料使用條款才能繼續使用系統。'
            ]);
        }

        UserAgreement::setAgreement($user, true);

        return redirect()->route('dashboard')->with('success', '感謝您同意個人資料使用條款。');
    }
}
