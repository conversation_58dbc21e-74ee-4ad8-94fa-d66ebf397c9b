<?php

namespace App\Http\Middleware;

use App\Models\SystemSetting;
use App\Models\SystemLog;
use App\Models\User;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

/**
 * 系統時序檢查中間件
 * 
 * 檢查系統開放時間和招生期間限制
 */
class CheckSystemTiming
{
    /**
     * 處理傳入的請求
     *
     * @param Request $request
     * @param Closure $next
     * @param string ...$permissions 需要檢查的權限
     * @return Response
     */
    public function handle(Request $request, Closure $next, string ...$permissions): Response
    {
        /** @var User|null $user */
        $user = Auth::user();

        // 管理員不受時序限制
        if ($user && $user->isAdmin()) {
            return $next($request);
        }

        // 檢查系統是否開放
        if (in_array('system_open', $permissions) && !SystemSetting::isSystemOpen()) {
            SystemLog::logOperation(
                SystemLog::ACTION_VIEW,
                '嘗試在系統關閉期間訪問',
                [
                    'user_id' => $user?->id,
                    'user_role' => $user?->role,
                    'requested_url' => $request->url(),
                ],
                SystemLog::LEVEL_WARNING
            );

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => '系統目前未開放，請稍後再試',
                    'error_code' => 'SYSTEM_CLOSED'
                ], 503);
            }

            return redirect()->route('home')->withErrors([
                'system' => '系統目前未開放，請稍後再試'
            ]);
        }

        // 檢查招生期間限制
        if (in_array('recruitment_period', $permissions)) {
            $isRecruitmentPeriod = SystemSetting::isRecruitmentPeriod();

            // 如果在招生期間，某些功能可能被限制
            if ($isRecruitmentPeriod && in_array('no_edit_during_recruitment', $permissions)) {
                SystemLog::logOperation(
                    SystemLog::ACTION_VIEW,
                    '嘗試在招生期間進行被限制的操作',
                    [
                        'user_id' => $user?->id,
                        'user_role' => $user?->role,
                        'requested_url' => $request->url(),
                        'action' => 'edit_during_recruitment',
                    ],
                    SystemLog::LEVEL_WARNING
                );

                if ($request->expectsJson()) {
                    return response()->json([
                        'success' => false,
                        'message' => '招生期間無法進行此操作',
                        'error_code' => 'RECRUITMENT_PERIOD_RESTRICTION'
                    ], 403);
                }

                return back()->withErrors([
                    'system' => '招生期間無法進行此操作'
                ]);
            }
        }

        // 檢查問卷編輯鎖定
        if (in_array('questionnaire_edit', $permissions) && SystemSetting::isQuestionnaireEditLocked()) {
            SystemLog::logOperation(
                SystemLog::ACTION_VIEW,
                '嘗試編輯被鎖定的問卷',
                [
                    'user_id' => $user?->id,
                    'user_role' => $user?->role,
                    'requested_url' => $request->url(),
                ],
                SystemLog::LEVEL_WARNING
            );

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => '問卷編輯功能目前被鎖定',
                    'error_code' => 'QUESTIONNAIRE_LOCKED'
                ], 403);
            }

            return back()->withErrors([
                'system' => '問卷編輯功能目前被鎖定'
            ]);
        }

        return $next($request);
    }
}
