<?php

use App\Http\Controllers\Auth\ApplicantLoginController;
use App\Http\Controllers\Auth\RecommenderAuthController;
use App\Http\Controllers\Api\AdmissionInfoController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API 路由
|--------------------------------------------------------------------------
|
| 注意：Laravel 會自動為 api.php 添加 /api 前綴
|
*/

/**
 * 招生系統考生登入
 * 
 * 將收到的 token 送回 API系統 進行解碼和驗證
 * 取得考生資料後登入後建立或更新考生記錄，並登入系統
 */
Route::get('/auth-from-external', [ApplicantLoginController::class, 'handleExternalAuth'])
    ->middleware(['web'])
    ->name('api.auth.external');
