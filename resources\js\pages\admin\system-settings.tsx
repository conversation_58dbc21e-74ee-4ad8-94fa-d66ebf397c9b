import AdminLayout from '@/layouts/admin-layout';
import { Head } from '@inertiajs/react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { router, useForm } from '@inertiajs/react';
import { Save, AlertTriangle, RefreshCw } from 'lucide-react';
import { useState } from 'react';

interface SystemSetting {
    id: number;
    key: string;
    value: string;
    type: string;
    category: string;
    description: string;
    is_active: boolean;
}

interface SystemSettingsProps {
    settings: {
        timing: SystemSetting[];
        general: SystemSetting[];
        security: SystemSetting[];
        email: SystemSetting[];
    };
    system_status: {
        is_system_open: boolean;
        is_recruitment_period: boolean;
        is_questionnaire_locked: boolean;
        reminder_cooldown_hours: number;
        auto_timeout_days: number;
        recruitment_period: {
            start: string | null;
            end: string | null;
            is_active: boolean;
        };
    };
}

export default function SystemSettings({ settings, system_status }: SystemSettingsProps) {
    const [isLoading, setIsLoading] = useState(false);

    // 格式化日期時間為 HTML datetime-local 格式
    const formatDateTimeLocal = (dateString: string) => {
        if (!dateString) return '';
        try {
            const date = new Date(dateString);
            return date.toISOString().slice(0, 16); // YYYY-MM-DDTHH:mm
        } catch {
            return '';
        }
    };

    const { data, setData, processing, errors } = useForm({
        system_open_time: formatDateTimeLocal(settings?.timing.find((setting) => setting.key === 'system.open_time')?.value || ''),
        system_close_time: formatDateTimeLocal(settings?.timing.find((setting) => setting.key === 'system.close_time')?.value || ''),
        recruitment_period_start: formatDateTimeLocal(settings?.timing.find((setting) => setting.key === 'recruitment.period_start')?.value || ''),
        recruitment_period_end: formatDateTimeLocal(settings?.timing.find((setting) => setting.key === 'recruitment.period_end')?.value || ''),
        reminder_cooldown: settings?.timing.find((setting) => setting.key === 'reminder.cooldown_hours')?.value || '24',
        auto_timeout: settings?.timing.find((setting) => setting.key === 'auto.timeout_days')?.value || '0',
        questionnaire_locked: settings?.general.find((setting) => setting.key === 'questionnaire.edit_locked')?.value === '1',
    });

    // 麵包屑導航
    const breadcrumbs = [
        { title: '儀表板', href: '/dashboard' },
        { title: '系統設定', href: '/admin/system-settings' },
    ];

    const handleSaveSettings = () => {
        // 使用 PUT 方法更新設定
        router.put('/admin/system-settings', data, {
            onSuccess: () => {
                alert('設定已儲存');
            },
            onError: (errors) => {
                console.error('儲存失敗:', errors);
                alert('儲存失敗，請檢查輸入資料');
            },
        });
    };

    const handleResetDefaults = () => {
        if (confirm('確定要重置所有設定為預設值嗎？此操作無法復原。')) {
            setIsLoading(true);
            router.post(
                '/admin/system-settings/reset-defaults',
                {},
                {
                    onFinish: () => setIsLoading(false),
                    onSuccess: () => {
                        alert('設定已重置為預設值');
                        router.reload();
                    },
                    onError: () => {
                        alert('重置失敗，請重試');
                    },
                },
            );
        }
    };

    const toggleQuestionnaireLock = (checked: boolean) => {
        setIsLoading(true);
        setData('questionnaire_locked', checked);

        router.post(
            '/admin/system-settings/toggle-questionnaire-lock',
            { locked: checked },
            {
                onFinish: () => setIsLoading(false),
                onSuccess: () => {
                    // 不需要重新載入頁面，狀態已經更新
                },
                onError: () => {
                    // 如果失敗，恢復原狀態
                    setData('questionnaire_locked', !checked);
                    alert('操作失敗，請重試');
                },
            },
        );
    };

    return (
        <AdminLayout breadcrumbs={breadcrumbs} title="系統設定" description="管理系統的各項參數和配置">
            <Head title="系統設定" />

            <div className="space-y-6 p-6">
                {/* 操作按鈕區域 */}
                <div className="flex justify-end gap-2">
                    <Button variant="outline" size="sm" onClick={handleResetDefaults} disabled={isLoading}>
                        <RefreshCw className="mr-1 h-3 w-3" />
                        <span className="text-xs">重置預設值</span>
                    </Button>
                    <Button size="sm" onClick={handleSaveSettings} disabled={processing}>
                        <Save className="mr-1 h-3 w-3" />
                        <span className="text-xs">儲存設定</span>
                    </Button>
                </div>

                {/* 系統狀態概覽 */}
                <Card>
                    <CardHeader>
                        <CardTitle className="text-base">系統狀態概覽</CardTitle>
                        <CardDescription className="text-xs">目前系統的運行狀態</CardDescription>
                    </CardHeader>
                    <CardContent className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
                        <div className="flex items-center justify-between rounded-lg border p-4">
                            <div className="flex items-center gap-3">
                                <div className={`h-3 w-3 rounded-full ${system_status.is_system_open ? 'bg-green-500' : 'bg-red-500'}`}></div>
                                <span className="text-sm font-medium">系統狀態</span>
                            </div>
                            <span
                                className={`rounded-full px-3 py-1 text-xs font-medium ${system_status.is_system_open ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}
                            >
                                {system_status.is_system_open ? '開放中' : '關閉中'}
                            </span>
                        </div>

                        <div className="flex items-center justify-between rounded-lg border p-4">
                            <div className="flex items-center gap-3">
                                <div className={`h-3 w-3 rounded-full ${system_status.is_recruitment_period ? 'bg-blue-500' : 'bg-gray-400'}`}></div>
                                <span className="text-sm font-medium">招生期間</span>
                            </div>
                            <span
                                className={`rounded-full px-3 py-1 text-xs font-medium ${system_status.is_recruitment_period ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'}`}
                            >
                                {system_status.is_recruitment_period ? '進行中' : '非招生期間'}
                            </span>
                        </div>

                        <div className="flex items-center justify-between rounded-lg border p-4">
                            <div className="flex items-center gap-3">
                                <div
                                    className={`h-3 w-3 rounded-full ${system_status.is_questionnaire_locked ? 'bg-red-500' : 'bg-green-500'}`}
                                ></div>
                                <span className="text-sm font-medium">問卷編輯</span>
                            </div>
                            <span
                                className={`rounded-full px-3 py-1 text-xs font-medium ${system_status.is_questionnaire_locked ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'}`}
                            >
                                {system_status.is_questionnaire_locked ? '已鎖定' : '可編輯'}
                            </span>
                        </div>

                        <div className="rounded-lg border p-4">
                            <div className="space-y-2">
                                <span className="text-sm font-medium text-gray-900">當前時間</span>
                                <p className="text-xs text-gray-600">
                                    {new Date().toLocaleString('zh-TW', {
                                        year: 'numeric',
                                        month: '2-digit',
                                        day: '2-digit',
                                        hour: '2-digit',
                                        minute: '2-digit',
                                        second: '2-digit',
                                    })}
                                </p>
                            </div>
                        </div>

                        <div className="col-span-full rounded-lg border bg-gray-50 p-4">
                            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                <div className="space-y-2">
                                    <span className="text-sm font-medium text-gray-900">招生時程</span>
                                    <div className="space-y-1">
                                        <div className="flex justify-between text-xs">
                                            <span className="text-gray-600">開始時間</span>
                                            <span className="font-medium">
                                                {system_status.recruitment_period?.start
                                                    ? new Date(system_status.recruitment_period.start).toLocaleString('zh-TW')
                                                    : '未設定'}
                                            </span>
                                        </div>
                                        <div className="flex justify-between text-xs">
                                            <span className="text-gray-600">結束時間</span>
                                            <span className="font-medium">
                                                {system_status.recruitment_period?.end
                                                    ? new Date(system_status.recruitment_period.end).toLocaleString('zh-TW')
                                                    : '未設定'}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div className="space-y-2">
                                    <span className="text-sm font-medium text-gray-900">系統參數</span>
                                    <div className="space-y-1">
                                        <div className="flex justify-between text-xs">
                                            <span className="text-gray-600">提醒冷卻時間</span>
                                            <span className="font-medium">{system_status.reminder_cooldown_hours} 小時</span>
                                        </div>
                                        <div className="flex justify-between text-xs">
                                            <span className="text-gray-600">自動超時天數</span>
                                            <span className="font-medium">{system_status.auto_timeout_days} 天</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* 時序設定 */}
                <Card>
                    <CardHeader>
                        <CardTitle className="text-base">時序設定</CardTitle>
                        <CardDescription className="text-xs">控制系統的開放時間和自動化行為</CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-6">
                        <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                            <div className="space-y-2">
                                <Label htmlFor="system_open_time">系統開放時間</Label>
                                <Input
                                    id="system_open_time"
                                    type="datetime-local"
                                    value={data.system_open_time}
                                    onChange={(e) => setData('system_open_time', e.target.value)}
                                />
                                {errors.system_open_time && <p className="text-sm text-red-600">{errors.system_open_time}</p>}
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="system_close_time">系統關閉時間</Label>
                                <Input
                                    id="system_close_time"
                                    type="datetime-local"
                                    value={data.system_close_time}
                                    onChange={(e) => setData('system_close_time', e.target.value)}
                                />
                                {errors.system_close_time && <p className="text-sm text-red-600">{errors.system_close_time}</p>}
                            </div>
                        </div>

                        <Separator />

                        {/* 招生時程設定 */}
                        <div className="space-y-4">
                            <div>
                                <h4 className="text-sm font-medium text-gray-900">招生時程設定</h4>
                                <p className="text-xs text-gray-600">設定招生期間的開始和結束時間</p>
                            </div>
                            <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                                <div className="space-y-2">
                                    <Label htmlFor="recruitment_period_start">招生開始時間</Label>
                                    <Input
                                        id="recruitment_period_start"
                                        type="datetime-local"
                                        value={data.recruitment_period_start}
                                        onChange={(e) => setData('recruitment_period_start', e.target.value)}
                                    />
                                    {errors.recruitment_period_start && <p className="text-sm text-red-600">{errors.recruitment_period_start}</p>}
                                </div>
                                <div className="space-y-2">
                                    <Label htmlFor="recruitment_period_end">招生結束時間</Label>
                                    <Input
                                        id="recruitment_period_end"
                                        type="datetime-local"
                                        value={data.recruitment_period_end}
                                        onChange={(e) => setData('recruitment_period_end', e.target.value)}
                                    />
                                    {errors.recruitment_period_end && <p className="text-sm text-red-600">{errors.recruitment_period_end}</p>}
                                </div>
                            </div>
                        </div>

                        <Separator />

                        <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                            <div className="space-y-2">
                                <Label htmlFor="reminder_cooldown">提醒冷卻時間 (小時)</Label>
                                <Input
                                    id="reminder_cooldown"
                                    type="number"
                                    min="1"
                                    max="168"
                                    value={data.reminder_cooldown}
                                    onChange={(e) => setData('reminder_cooldown', e.target.value)}
                                />
                                <p className="text-xs text-muted-foreground">發送提醒後需等待的時間間隔</p>
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="auto_timeout">自動超時天數</Label>
                                <Input
                                    id="auto_timeout"
                                    type="number"
                                    min="1"
                                    max="30"
                                    value={data.auto_timeout}
                                    onChange={(e) => setData('auto_timeout', e.target.value)}
                                />
                                <p className="text-xs text-muted-foreground">推薦函自動標記為超時的天數</p>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* 一般設定 */}
                <Card>
                    <CardHeader>
                        <CardTitle>一般設定</CardTitle>
                        <CardDescription>系統的基本功能設定</CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-6">
                        <div className="flex items-center justify-between">
                            <div className="space-y-0.5">
                                <Label>問卷模板編輯鎖定</Label>
                                <p className="text-sm text-muted-foreground">
                                    鎖定後將無法編輯問卷模板
                                    {data.questionnaire_locked && (
                                        <span className="ml-2 inline-flex items-center rounded-full bg-red-100 px-2 py-1 text-xs font-medium text-red-800">
                                            已鎖定
                                        </span>
                                    )}
                                </p>
                            </div>
                            <div className="flex items-center gap-3">
                                <span className={`text-sm font-medium ${data.questionnaire_locked ? 'text-red-600' : 'text-green-600'}`}>
                                    {data.questionnaire_locked ? '鎖定' : '開放'}
                                </span>
                                <Switch
                                    checked={data.questionnaire_locked}
                                    onCheckedChange={toggleQuestionnaireLock}
                                    disabled={isLoading}
                                    className={data.questionnaire_locked ? 'data-[state=checked]:bg-red-600' : 'data-[state=checked]:bg-green-600'}
                                />
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* 警告訊息 */}
                <Card className="border-orange-200 bg-orange-50">
                    <CardContent>
                        <div className="flex items-start gap-3">
                            <AlertTriangle className="mt-0.5 h-5 w-5 text-orange-600" />
                            <div className="space-y-1">
                                <p className="text-sm font-medium text-orange-800">注意事項</p>
                                <p className="text-sm text-orange-700">
                                    修改系統設定可能會影響正在進行的申請流程，請謹慎操作。 建議在非招生期間進行重要設定的變更。
                                </p>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </AdminLayout>
    );
}
