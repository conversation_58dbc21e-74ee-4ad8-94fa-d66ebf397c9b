import AdminLayout from '@/layouts/admin-layout';
import { Head } from '@inertiajs/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Search, Filter, Download, Eye, Activity, AlertTriangle, Info, CheckCircle } from 'lucide-react';
import { useState } from 'react';

interface SystemLog {
    id: number;
    user_id?: number;
    action: string;
    description: string;
    level: string;
    ip_address: string;
    user_agent: string;
    context?: any;
    created_at: string;
    user?: {
        id: number;
        name: string;
        email: string;
        role: string;
    };
}

interface SystemLogsProps {
    logs: {
        data: SystemLog[];
        current_page: number;
        last_page: number;
        total: number;
    };
}

export default function SystemLogs({ logs }: SystemLogsProps) {
    const [searchTerm, setSearchTerm] = useState('');
    const [levelFilter, setLevelFilter] = useState('all');
    const [actionFilter, setActionFilter] = useState('all');

    // 麵包屑導航
    const breadcrumbs = [
        { title: '儀表板', href: '/dashboard' },
        { title: '系統日誌', href: '/admin/system-logs' },
    ];

    // 日誌等級徽章
    const getLevelBadge = (level: string | undefined | null) => {
        if (!level) {
            return <Badge variant="secondary">未知</Badge>;
        }
        switch (level.toLowerCase()) {
            case 'error':
                return (
                    <Badge variant="secondary" className="flex items-center gap-1 bg-red-100 text-red-800">
                        <AlertTriangle className="h-3 w-3" />
                        錯誤
                    </Badge>
                );
            case 'warning':
                return (
                    <Badge variant="secondary" className="flex items-center gap-1 bg-yellow-100 text-yellow-800">
                        <AlertTriangle className="h-3 w-3" />
                        警告
                    </Badge>
                );
            case 'info':
                return (
                    <Badge variant="secondary" className="flex items-center gap-1 bg-blue-100 text-blue-800">
                        <Info className="h-3 w-3" />
                        資訊
                    </Badge>
                );
            case 'success':
                return (
                    <Badge variant="secondary" className="flex items-center gap-1 bg-green-100 text-green-800">
                        <CheckCircle className="h-3 w-3" />
                        成功
                    </Badge>
                );
            default:
                return <Badge variant="secondary">{level}</Badge>;
        }
    };

    // 操作類型徽章
    const getActionBadge = (action: string) => {
        const actionMap: { [key: string]: { label: string; color: string } } = {
            CREATE: { label: '新增', color: 'bg-green-100 text-green-800' },
            UPDATE: { label: '更新', color: 'bg-blue-100 text-blue-800' },
            DELETE: { label: '刪除', color: 'bg-red-100 text-red-800' },
            VIEW: { label: '查看', color: 'bg-gray-100 text-gray-800' },
            LOGIN: { label: '登入', color: 'bg-purple-100 text-purple-800' },
            LOGOUT: { label: '登出', color: 'bg-orange-100 text-orange-800' },
            EXPORT: { label: '匯出', color: 'bg-indigo-100 text-indigo-800' },
            IMPORT: { label: '匯入', color: 'bg-pink-100 text-pink-800' },
        };

        const actionInfo = actionMap[action.toUpperCase()] || { label: action, color: 'bg-gray-100 text-gray-800' };

        return (
            <Badge variant="secondary" className={actionInfo.color}>
                {actionInfo.label}
            </Badge>
        );
    };

    // 過濾日誌
    const filteredLogs = logs.data.filter((log) => {
        const matchesSearch =
            log.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
            log.user?.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
            log.user?.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
            log.action?.toLowerCase().includes(searchTerm.toLowerCase());

        const matchesLevel = levelFilter === 'all' || log.level?.toLowerCase() === levelFilter;
        const matchesAction = actionFilter === 'all' || log.action?.toUpperCase() === actionFilter;

        return matchesSearch && matchesLevel && matchesAction;
    });

    // 獲取所有操作類型
    const actions = Array.from(new Set(logs.data.map((log) => log.action?.toUpperCase() || 'UNKNOWN').filter((action) => action !== 'UNKNOWN')));

    return (
        <AdminLayout breadcrumbs={breadcrumbs} title="系統日誌" description="查看和管理系統操作記錄和錯誤日誌">
            <Head title="系統日誌" />

            <div className="space-y-6 p-6">
                {/* 篩選和搜尋區域 */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <Filter className="h-5 w-5" />
                            篩選和搜尋
                        </CardTitle>
                        <CardDescription>使用下方工具來篩選和搜尋系統日誌</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
                            <div className="relative">
                                <Search className="absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 transform text-gray-400" />
                                <Input
                                    placeholder="搜尋描述、使用者或操作..."
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                    className="pl-10"
                                />
                            </div>
                            <Select value={levelFilter} onValueChange={setLevelFilter}>
                                <SelectTrigger>
                                    <SelectValue placeholder="選擇等級" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">所有等級</SelectItem>
                                    <SelectItem value="error">錯誤</SelectItem>
                                    <SelectItem value="warning">警告</SelectItem>
                                    <SelectItem value="info">資訊</SelectItem>
                                    <SelectItem value="success">成功</SelectItem>
                                </SelectContent>
                            </Select>
                            <Select value={actionFilter} onValueChange={setActionFilter}>
                                <SelectTrigger>
                                    <SelectValue placeholder="選擇操作" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">所有操作</SelectItem>
                                    {actions.map((action) => (
                                        <SelectItem key={action} value={action}>
                                            {action}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                            <Button variant="outline" size="sm" className="flex items-center gap-1">
                                <Download className="h-3 w-3" />
                                <span className="text-xs">匯出日誌</span>
                            </Button>
                        </div>
                    </CardContent>
                </Card>

                {/* 統計資訊 */}
                <div className="grid grid-cols-1 gap-4 md:grid-cols-5">
                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center gap-2">
                                <Activity className="h-5 w-5 text-gray-500" />
                                <div>
                                    <div className="text-2xl font-bold">{logs.total}</div>
                                    <p className="text-xs text-muted-foreground">總日誌數</p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardContent className="p-6">
                            <div className="text-2xl font-bold text-red-600">
                                {logs.data.filter((log) => log.level?.toLowerCase() === 'error').length}
                            </div>
                            <p className="text-xs text-muted-foreground">錯誤</p>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardContent className="p-6">
                            <div className="text-2xl font-bold text-yellow-600">
                                {logs.data.filter((log) => log.level?.toLowerCase() === 'warning').length}
                            </div>
                            <p className="text-xs text-muted-foreground">警告</p>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardContent className="p-6">
                            <div className="text-2xl font-bold text-blue-600">
                                {logs.data.filter((log) => log.level?.toLowerCase() === 'info').length}
                            </div>
                            <p className="text-xs text-muted-foreground">資訊</p>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardContent className="p-6">
                            <div className="text-2xl font-bold text-green-600">
                                {logs.data.filter((log) => log.level?.toLowerCase() === 'success').length}
                            </div>
                            <p className="text-xs text-muted-foreground">成功</p>
                        </CardContent>
                    </Card>
                </div>

                {/* 日誌列表 */}
                <Card>
                    <CardHeader>
                        <CardTitle>系統日誌記錄</CardTitle>
                        <CardDescription>
                            顯示 {filteredLogs.length} / {logs.data.length} 筆記錄
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            {filteredLogs.map((log) => (
                                <div key={log.id} className="flex items-start justify-between rounded-lg border p-4 hover:bg-gray-50">
                                    <div className="flex-1 space-y-2">
                                        <div className="flex items-center gap-3">
                                            {getLevelBadge(log.level)}
                                            {getActionBadge(log.action)}
                                            <span className="text-sm text-gray-500">{new Date(log.created_at).toLocaleString('zh-TW')}</span>
                                        </div>
                                        <p className="font-medium text-gray-900">{log.description}</p>
                                        <div className="grid grid-cols-1 gap-4 text-sm text-gray-500 md:grid-cols-3">
                                            <div>
                                                <span className="font-medium">使用者：</span>
                                                {log.user ? `${log.user.name} (${log.user.email})` : '系統'}
                                            </div>
                                            <div>
                                                <span className="font-medium">IP 位址：</span>
                                                {log.ip_address}
                                            </div>
                                            <div>
                                                <span className="font-medium">ID：</span>#{log.id}
                                            </div>
                                        </div>
                                        {log.context && (
                                            <details className="text-sm">
                                                <summary className="cursor-pointer text-blue-600 hover:text-blue-800">查看詳細資訊</summary>
                                                <pre className="mt-2 overflow-x-auto rounded bg-gray-100 p-2 text-xs">
                                                    {JSON.stringify(log.context, null, 2)}
                                                </pre>
                                            </details>
                                        )}
                                    </div>
                                    <div className="ml-4 flex gap-2">
                                        <Button variant="outline" size="sm">
                                            <Eye className="h-4 w-4" />
                                        </Button>
                                    </div>
                                </div>
                            ))}

                            {filteredLogs.length === 0 && <div className="py-8 text-center text-gray-500">沒有找到符合條件的系統日誌</div>}
                        </div>
                    </CardContent>
                </Card>
            </div>
        </AdminLayout>
    );
}
