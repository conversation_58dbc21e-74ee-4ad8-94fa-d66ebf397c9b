<?php

namespace App\Services;

use App\Models\User;
use App\Models\Applicant;
use App\Models\Recommender;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;

/**
 * 統一認證服務類
 */
class AuthenticationService
{
    /**
     * 申請人 JWT 登入處理
     * 
     * 將收到的 token 送回 API 系統交換所需的考生資料後，代理考生登入系統
     *
     * @param string $token JWT Token
     * @param string $clientIp 客戶端 IP
     * @return array 登入結果
     */
    public function verifyTokenWithApi(string $token, string $clientIp): array
    {
        try {
            $userData = $this->fetchUserDataFromApiSystem($token);

            if (!$userData) {
                throw new \Exception('無法從原系統取得使用者資訊');
            }

            $user = $this->createUserFromExternalData($userData);

            $external_uid = $userData['stu_idno'] ?? null;
            $stu_year = $userData['stu_year'] ?? null;
            $exam_id = $userData['exam_id'] ?? null;
            $applicant = $this->createApplicantRecord($user, $external_uid, $stu_year, $exam_id, $userData);

            $this->performLogin($user, [
                'applicant_id' => $applicant->id,
                'external_uid' => $external_uid, // 加密的考生 ID
                'exam_year' => $stu_year,
                'exam_id' => $exam_id,
                'login_method' => 'jwt',
            ]);

            Log::info('申請人 JWT 登入成功', [
                'external_uid' => $external_uid,
                'exam_year' => $stu_year,
                'exam_id' => $exam_id,
                'user_id' => $user->id,
                'applicant_id' => $applicant->id,
                'ip' => $clientIp,
            ]);

            return [
                'success' => true,
                'message' => '登入成功',
                'user' => $user,
                'applicant' => $applicant,
            ];
        } catch (\Exception $e) {
            Log::error('申請人 JWT 登入失敗', [
                'error' => $e->getMessage(),
                'ip' => $clientIp,
            ]);

            return [
                'success' => false,
                'message' => '登入失敗：' . $e->getMessage(),
            ];
        }
    }


    private function fetchUserDataFromApiSystem(string $token): array
    {
        $apiUrl = 'http://localhost:18001/index.php/api/v1/recommendation_system/get_applicant_data'; // 需要替換為實際的 API URL
        $apiSecret = 'OdmzICpznuM7O48V3gJCJaejNWwabcpG'; // 需要替換為實際的 API 密鑰

        $client = new Client();

        try {
            $response = $client->request('POST', $apiUrl, [
                'form_params' => [
                    'token' => $token,
                    'nuu_api_key' => $apiSecret,
                    'nuu_api_id' => 'get_applicant_data',
                ],
            ]);

            // 解析 JSON 回應
            $data = json_decode($response->getBody(), true);

            // 檢查 API 是否成功
            if (!isset($data['status']) || $data['status'] !== 'success') {
                throw new \Exception('API 回傳非 success：' . json_encode($data));
            }

            return $data['data']; // 這是你要的學生資料陣列

        } catch (GuzzleException $e) {
            Log::error('API 請求錯誤', [
                'error' => $e->getMessage(),
                'token' => substr($token, 0, 20) . '...',
            ]);
            throw new \Exception('無法從 API 系統取得使用者資料');
        }
    }

    /**
     * 推薦人 Token 登入處理
     * 
     * @param string $token 登入 Token
     * @param string $clientIp 客戶端 IP
     * @return array 登入結果
     */
    public function loginRecommenderByToken(string $token, string $clientIp): array
    {
        try {
            // 1. 驗證 Token 並查找推薦人
            $recommender = Recommender::where('login_token', $token)
                ->where(function ($query) {
                    $query->whereNull('token_expires_at')
                        ->orWhere('token_expires_at', '>', now());
                })
                ->first();

            if (!$recommender) {
                return [
                    'success' => false,
                    'message' => 'Token 無效或已過期',
                ];
            }

            // 2. 查找或建立使用者帳號
            $user = $this->findOrCreateUser([
                'email' => $recommender->email,
                'name' => $recommender->name,
                'role' => 'recommender',
            ]);

            // 3. 確保推薦人與使用者關聯
            if (!$recommender->user_id) {
                $recommender->update(['user_id' => $user->id]);
            }

            // 4. 更新最後登入時間
            $recommender->updateLastLogin();

            // 5. 執行登入並設定 session
            $this->performLogin($user, [
                'recommender_id' => $recommender->id,
                'login_token' => $token,
            ]);

            // 6. 記錄登入日誌
            Log::info('推薦人登入成功', [
                'recommender_id' => $recommender->id,
                'user_id' => $user->id,
                'token' => $token,
                'ip' => $clientIp,
            ]);

            return [
                'success' => true,
                'user' => $user,
                'recommender' => $recommender,
                'message' => '登入成功',
            ];
        } catch (\Exception $e) {
            Log::error('推薦人登入失敗', [
                'token' => $token,
                'error' => $e->getMessage(),
                'ip' => $clientIp,
            ]);

            return [
                'success' => false,
                'message' => '登入失敗：' . $e->getMessage(),
            ];
        }
    }

    /**
     * 統一登出處理
     * 
     * @param string $userType 使用者類型 (applicant|recommender|admin)
     * @return void
     */
    public function logout(string $userType = 'general'): void
    {
        // 根據使用者類型清除特定的 session 資料
        switch ($userType) {
            case 'applicant':
                Session::forget(['applicant_id', 'external_uid']);
                break;
            case 'recommender':
                Session::forget(['recommender_id', 'login_token']);
                break;
            case 'admin':
            default:
                // 一般登出，清除所有相關 session
                Session::forget(['applicant_id', 'external_uid', 'recommender_id', 'login_token']);
                break;
        }

        // 執行 Laravel 標準登出流程
        Auth::logout();
        request()->session()->invalidate();
        request()->session()->regenerateToken();

        Log::info('使用者登出', [
            'user_type' => $userType,
            'ip' => request()->ip(),
        ]);
    }

    /**
     * 查找或建立使用者帳號
     * 
     * @param array $userData 使用者資料
     * @return User
     */
    private function findOrCreateUser(array $userData): User
    {
        $user = User::where('email', $userData['email'])->first();

        if (!$user) {
            $user = User::create([
                'name' => $userData['name'],
                'email' => $userData['email'],
                'role' => $userData['role'],
            ]);

            Log::info('建立新使用者帳號', [
                'user_id' => $user->id,
                'email' => $userData['email'],
                'role' => $userData['role'],
            ]);
        }

        return $user;
    }

    /**
     * 執行登入並設定 session
     * 
     * @param User $user 使用者物件
     * @param array $sessionData 額外的 session 資料
     * @return void
     */
    private function performLogin(User $user, array $sessionData = []): void
    {
        // 執行 Laravel 認證登入
        Auth::login($user);

        // 重新生成 session ID 以防止 session fixation 攻擊
        // 檢查是否有 session 可用
        if (request()->hasSession()) {
            request()->session()->regenerate();
        }

        // 設定額外的 session 資料
        foreach ($sessionData as $key => $value) {
            Session::put($key, $value);
        }

        Log::debug('登入 session 設定完成', [
            'user_id' => $user->id,
            'session_data' => array_keys($sessionData),
            'has_session' => request()->hasSession(),
        ]);
    }

    /**
     * 將外部取得的使用者資料建立使用者
     *
     * @param array $userData 外部使用者資料
     * @return User 建立的使用者
     */
    private function createUserFromExternalData(array $userData): User
    {
        $user = User::create([
            'name' => $userData['stu_name'],
            'email' => $userData['stu_e_mail'],
            'role' => 'applicant',
        ]);

        Log::info('從外部資料建立新使用者', [
            'user_id' => $user->id,
            'name' => $user->name,
            'email' => $user->email,
        ]);

        return $user;
    }

    /**
     * 建立申請人記錄
     *
     * @param User $user 使用者物件
     * @param string $stu_idno 學號
     * @param string $stu_year 學年度
     * @param string $exam_id 招生類別ID
     * @param array $userData 使用者資料
     * @return Applicant 建立的申請人記錄
     */
    private function createApplicantRecord(User $user, string $external_uid, string $stu_year, string $exam_id, array $userData): Applicant
    {
        $applicant = Applicant::create([
            'user_id' => $user->id,
            'external_uid' => $external_uid,
            'exam_year' => $stu_year,
            'exam_id' => $exam_id,
            'phone' => $userData['stu_cell_phone'] ?? null,
        ]);

        Log::info('建立新申請人記錄', [
            'user_id' => $user->id,
            'applicant_id' => $applicant->id,
            'external_uid' => $external_uid,
            'exam_year' => $stu_year,
            'exam_id' => $exam_id,
        ]);

        return $applicant;
    }
}
