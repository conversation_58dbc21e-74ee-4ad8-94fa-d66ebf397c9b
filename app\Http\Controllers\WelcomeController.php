<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;

/**
 * 歡迎頁面控制器
 * 處理首頁顯示和招生資訊取得
 */
class WelcomeController extends Controller
{
    /**
     * 顯示歡迎頁面
     * 
     * @param Request $request
     * @return \Inertia\Response
     */
    public function index(Request $request)
    {
        // 取得招生資訊
        // $admissionInfo = $this->getAdmissionInfo();

        return Inertia::render('welcome', [
            // 'admission_info' => $admissionInfo,
            'admission_info' => [], // 暫時不使用外部 API，返回空陣列
        ]);
    }
}
