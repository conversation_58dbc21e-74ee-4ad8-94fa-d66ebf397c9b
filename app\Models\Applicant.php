<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * 申請人模型
 *
 * 管理申請人的詳細資訊和第三方系統整合
 */
class Applicant extends Model
{
  use HasFactory;

  /**
   * 可批量賦值的屬性
   *
   * @var array<string>
   */
  protected $fillable = [
    'user_id',            // 關聯的使用者 ID
    'external_uid',       // 第三方系統 UID
    'exam_id',            // 考試ID
    'exam_year',           // 學年度
    'phone',              // 聯絡電話
    'examination_number', // 准考證號
  ];

  /**
   * 取得申請人關聯的使用者
   *
   * @return BelongsTo
   */
  public function user(): BelongsTo
  {
    return $this->belongsTo(User::class);
  }

  /**
   * 取得申請人的所有推薦函
   *
   * @return HasMany
   */
  public function recommendationLetters(): HasMany
  {
    return $this->hasMany(RecommendationLetter::class);
  }

  /**
   * 取得申請人的合併文件
   *
   * @return HasMany
   */
  public function mergedDocuments(): HasMany
  {
    return $this->hasMany(MergedDocument::class);
  }

  /**
   * 取得申請人的顯示名稱
   *
   * @return string
   */
  public function getDisplayNameAttribute(): string
  {
    return $this->user->name ?: $this->external_uid ?: '未知申請人';
  }

  /**
   * 取得申請人的電子郵件
   *
   * @return string|null
   */
  public function getEmailAttribute(): ?string
  {
    return $this->user->email ?? null;
  }

  /**
   * 根據考試資訊查找申請人
   *
   * @param string $exam_id
   * @param string $stu_year
   * @param string $stu_idno
   * @return static|null
   */
  public static function findByExamInfo(string $exam_id, string $stu_year, string $stu_idno): ?static
  {
    return static::where('exam_id', $exam_id)
      ->where('exam_year', $stu_year)
      ->where('external_uid', $stu_idno)
      ->first();
  }
}
