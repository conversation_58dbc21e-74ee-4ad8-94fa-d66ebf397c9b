<?php

use App\Http\Controllers\Auth\AuthenticatedSessionController;

use App\Http\Controllers\Auth\ApplicantLoginController;
use App\Http\Controllers\Auth\RecommenderAuthController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| 認證路由
|--------------------------------------------------------------------------
|
| 這裡定義所有與使用者認證相關的路由，針對不同角色的專用登入路由
|
*/

// 訪客路由 (未登入使用者可訪問)
Route::middleware('guest')->group(function () {
    // 登入功能
    /** 一般登入頁面 - 顯示登入表單 */
    Route::get('/login', [AuthenticatedSessionController::class, 'create'])
        ->name('login');

    /** 處理登入 - 驗證身份(管理員)並登入 */
    Route::post('/login', [AuthenticatedSessionController::class, 'store']);

    /** 推薦人跳轉登入(Token 基礎認證) */
    Route::get('/recommender/auth/{token}', [RecommenderAuthController::class, 'authenticateWithToken'])
        ->name('recommender.auth');
});

// 已認證使用者路由 (需要登入)
Route::middleware('auth')->group(function () {
    // 登出功能
    // todo 登出功能整合成一個路由，根據角色不同處理不同登出邏輯
    /** 管理員 */
    Route::post('/logout', [AuthenticatedSessionController::class, 'destroy'])
        ->name('logout');

    /** 申請人登出 */
    Route::post('/applicant/logout', [ApplicantLoginController::class, 'logout'])
        ->name('applicant.logout');

    /** 推薦人登出 */
    Route::post('/auth/recommender/logout', [RecommenderAuthController::class, 'logout'])
        ->name('recommender.logout');
});
