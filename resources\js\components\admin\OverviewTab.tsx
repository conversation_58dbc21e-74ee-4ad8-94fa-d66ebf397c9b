import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { router } from '@inertiajs/react';
import { 
    BarChart3, 
    FileText, 
    Settings, 
    Activity, 
    Mail, 
    ScanFace, 
    ArrowRight,
    Users,
    CheckCircle,
    Clock,
    AlertTriangle
} from 'lucide-react';

interface AdminRecommendation {
    id: number;
    status: string;
    created_at?: string;
}

interface OverviewTabProps {
    recommendations: AdminRecommendation[];
}

export default function OverviewTab({ recommendations }: OverviewTabProps) {
    // 統計資料
    const totalRecommendations = recommendations.length;
    const submittedCount = recommendations.filter(rec => rec.status === 'submitted').length;
    const pendingCount = recommendations.filter(rec => rec.status === 'pending').length;
    const declinedCount = recommendations.filter(rec => rec.status === 'declined').length;

    // 最近7天的推薦函
    const recentRecommendations = recommendations.filter(rec => {
        if (!rec.created_at) return false;
        const createdDate = new Date(rec.created_at);
        const sevenDaysAgo = new Date();
        sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
        return createdDate >= sevenDaysAgo;
    }).length;

    return (
        <div className="space-y-6 p-6">
            {/* 頁面標題 */}
            <div>
                <h1 className="text-3xl font-bold tracking-tight">管理員儀表板</h1>
                <p className="text-muted-foreground">系統概覽和快速操作</p>
            </div>

            {/* 統計卡片 */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">總推薦函數</CardTitle>
                        <FileText className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">{totalRecommendations}</div>
                        <p className="text-xs text-muted-foreground">
                            最近7天新增 {recentRecommendations} 筆
                        </p>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">已完成</CardTitle>
                        <CheckCircle className="h-4 w-4 text-green-600" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold text-green-600">{submittedCount}</div>
                        <p className="text-xs text-muted-foreground">
                            完成率 {totalRecommendations > 0 ? Math.round((submittedCount / totalRecommendations) * 100) : 0}%
                        </p>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">待處理</CardTitle>
                        <Clock className="h-4 w-4 text-yellow-600" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold text-yellow-600">{pendingCount}</div>
                        <p className="text-xs text-muted-foreground">
                            需要跟進處理
                        </p>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">已婉拒</CardTitle>
                        <AlertTriangle className="h-4 w-4 text-red-600" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold text-red-600">{declinedCount}</div>
                        <p className="text-xs text-muted-foreground">
                            婉拒率 {totalRecommendations > 0 ? Math.round((declinedCount / totalRecommendations) * 100) : 0}%
                        </p>
                    </CardContent>
                </Card>
            </div>

            {/* 快速操作 */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <Card className="cursor-pointer transition-shadow hover:shadow-md" onClick={() => router.get('/admin/recommendations')}>
                    <CardHeader className="pb-3">
                        <CardTitle className="flex items-center justify-between text-lg">
                            推薦函管理
                            <BarChart3 className="h-5 w-5 text-blue-500" />
                        </CardTitle>
                        <CardDescription>查看和管理所有推薦函申請</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="flex items-center justify-between">
                            <span className="text-sm text-gray-600">管理推薦函</span>
                            <ArrowRight className="h-4 w-4 text-gray-400" />
                        </div>
                    </CardContent>
                </Card>

                <Card className="cursor-pointer transition-shadow hover:shadow-md" onClick={() => router.get('/admin/questionnaire-templates')}>
                    <CardHeader className="pb-3">
                        <CardTitle className="flex items-center justify-between text-lg">
                            問卷模板
                            <FileText className="h-5 w-5 text-green-500" />
                        </CardTitle>
                        <CardDescription>管理推薦函問卷模板</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="flex items-center justify-between">
                            <span className="text-sm text-gray-600">模板管理</span>
                            <ArrowRight className="h-4 w-4 text-gray-400" />
                        </div>
                    </CardContent>
                </Card>

                <Card className="cursor-pointer transition-shadow hover:shadow-md" onClick={() => router.get('/admin/system-settings')}>
                    <CardHeader className="pb-3">
                        <CardTitle className="flex items-center justify-between text-lg">
                            系統設定
                            <Settings className="h-5 w-5 text-purple-500" />
                        </CardTitle>
                        <CardDescription>管理系統時序和參數設定</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="flex items-center justify-between">
                            <span className="text-sm text-gray-600">時序控制</span>
                            <ArrowRight className="h-4 w-4 text-gray-400" />
                        </div>
                    </CardContent>
                </Card>

                <Card className="cursor-pointer transition-shadow hover:shadow-md" onClick={() => router.get('/admin/system-logs')}>
                    <CardHeader className="pb-3">
                        <CardTitle className="flex items-center justify-between text-lg">
                            系統日誌
                            <Activity className="h-5 w-5 text-orange-500" />
                        </CardTitle>
                        <CardDescription>查看系統操作記錄</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="flex items-center justify-between">
                            <span className="text-sm text-gray-600">日誌監控</span>
                            <ArrowRight className="h-4 w-4 text-gray-400" />
                        </div>
                    </CardContent>
                </Card>

                <Card className="cursor-pointer transition-shadow hover:shadow-md" onClick={() => router.get('/admin/email-logs')}>
                    <CardHeader className="pb-3">
                        <CardTitle className="flex items-center justify-between text-lg">
                            Email 日誌
                            <Mail className="h-5 w-5 text-indigo-500" />
                        </CardTitle>
                        <CardDescription>查看 Email 發送記錄</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="flex items-center justify-between">
                            <span className="text-sm text-gray-600">信件記錄</span>
                            <ArrowRight className="h-4 w-4 text-gray-400" />
                        </div>
                    </CardContent>
                </Card>

                <Card className="cursor-pointer transition-shadow hover:shadow-md" onClick={() => router.get('/admin/login-logs')}>
                    <CardHeader className="pb-3">
                        <CardTitle className="flex items-center justify-between text-lg">
                            登入記錄
                            <ScanFace className="h-5 w-5 text-teal-500" />
                        </CardTitle>
                        <CardDescription>查看使用者登入記錄</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="flex items-center justify-between">
                            <span className="text-sm text-gray-600">安全監控</span>
                            <ArrowRight className="h-4 w-4 text-gray-400" />
                        </div>
                    </CardContent>
                </Card>
            </div>

            {/* 系統狀態 */}
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <Activity className="h-5 w-5" />
                        系統狀態
                    </CardTitle>
                    <CardDescription>目前系統運行狀態概覽</CardDescription>
                </CardHeader>
                <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div className="flex items-center justify-between p-3 border rounded-lg">
                            <span className="text-sm font-medium">系統狀態</span>
                            <Badge variant="secondary" className="bg-green-100 text-green-800">
                                正常運行
                            </Badge>
                        </div>
                        <div className="flex items-center justify-between p-3 border rounded-lg">
                            <span className="text-sm font-medium">資料庫連線</span>
                            <Badge variant="secondary" className="bg-green-100 text-green-800">
                                已連線
                            </Badge>
                        </div>
                        <div className="flex items-center justify-between p-3 border rounded-lg">
                            <span className="text-sm font-medium">Email 服務</span>
                            <Badge variant="secondary" className="bg-green-100 text-green-800">
                                正常
                            </Badge>
                        </div>
                    </div>
                </CardContent>
            </Card>
        </div>
    );
}
