<?php

namespace App\Http\Controllers;

use App\Models\QuestionnaireTemplate;
use App\Models\RecommendationLetter;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;

class QuestionnaireController extends Controller
{


    /**
     * Get questionnaire template for a specific recommendation.
     */
    public function getTemplate($recommendationId)
    {
        $user = Auth::user();

        $recommendation = RecommendationLetter::where('id', $recommendationId)
            ->where('recommender_email', $user->email)
            ->first();

        if (!$recommendation) {
            abort(404, '推薦函不存在或您無權限查看');
        }

        // Try to get specific template for this department and program type
        $template = QuestionnaireTemplate::getTemplate(
            $recommendation->department_name,
            $recommendation->program_type
        );

        // If no specific template, get default template
        if (!$template) {
            $template = QuestionnaireTemplate::getDefaultTemplate();
        }

        // If still no template, create a basic default one
        if (!$template) {
            $template = $this->createBasicTemplate();
        }

        // Ensure questions is parsed as array
        if ($template && is_string($template->questions)) {
            $template->questions = json_decode($template->questions, true);
        }

        return response()->json([
            'template' => $template,
            'recommendation' => $recommendation,
        ]);
    }

    /**
     * Upload and process CSV file to create questionnaire template.
     */
    public function uploadCsvTemplate(Request $request)
    {
        $request->validate([
            'csv_file' => 'required|file|mimes:csv,txt',
            'department_name' => 'required|string',
            'program_type' => 'required|string',
        ]);

        $file = $request->file('csv_file');
        $csvData = $this->parseCsvFile($file);

        if (empty($csvData)) {
            return back()->withErrors(['csv_file' => 'CSV 檔案格式錯誤或為空']);
        }

        $template = QuestionnaireTemplate::createFromCsv(
            $request->department_name,
            $request->program_type,
            $csvData
        );

        return back()->with([
            'success' => '問卷模板已成功創建',
            'template' => $template,
        ]);
    }

    /**
     * Create or update questionnaire template manually.
     */
    public function saveTemplate(Request $request)
    {
        $request->validate([
            'department_name' => 'required|string',
            'program_type' => 'required|string',
            'template_name' => 'required|string',
            'questions' => 'required|array',
            'questions.*.question' => 'required|string',
            'questions.*.type' => 'required|string|in:text,textarea,select,radio,checkbox',
        ]);

        // Deactivate existing templates for this department/program
        QuestionnaireTemplate::where('department_name', $request->department_name)
            ->where('program_type', $request->program_type)
            ->update(['is_active' => false]);

        QuestionnaireTemplate::create([
            'department_name' => $request->department_name,
            'program_type' => $request->program_type,
            'template_name' => $request->template_name,
            'questions' => $request->questions,
            'is_active' => true,
        ]);

        return redirect()->route('questionnaire.index')->with('success', '問卷模板已保存');
    }

    /**
     * 顯示問卷模板管理頁面
     */
    public function index()
    {
        $templates = QuestionnaireTemplate::orderBy('created_at', 'desc')->get();

        // Ensure questions is parsed as array for each template
        $templates->transform(function ($template) {
            if (is_string($template->questions)) {
                $template->questions = json_decode($template->questions, true);
            }
            return $template;
        });

        return Inertia::render('admin/questionnaire-templates', [
            'templates' => $templates,
            'isLocked' => \App\Models\SystemSetting::isQuestionnaireEditLocked(),
        ]);
    }

    /**
     * Delete a questionnaire template.
     */
    public function destroy($id)
    {
        try {
            $template = QuestionnaireTemplate::findOrFail($id);
            $template->update(['is_active' => false]);

            return back()->with('success', '問卷模板已刪除');
        } catch (\Exception $e) {
            return back()->withErrors([
                'delete' => '刪除問卷模板失敗：' . $e->getMessage()
            ]);
        }
    }

    /**
     * Parse CSV file and return array of question data.
     */
    private function parseCsvFile($file): array
    {
        $csvData = [];
        $handle = fopen($file->getPathname(), 'r');

        if ($handle === false) {
            return [];
        }

        // Read header row
        $headers = fgetcsv($handle);
        if (!$headers) {
            fclose($handle);
            return [];
        }

        // Read data rows
        while (($row = fgetcsv($handle)) !== false) {
            if (count($row) === count($headers)) {
                $csvData[] = array_combine($headers, $row);
            }
        }

        fclose($handle);
        return $csvData;
    }

    /**
     * Create a basic default template.
     */
    private function createBasicTemplate(): QuestionnaireTemplate
    {
        $basicQuestions = [
            [
                'id' => 'relationship',
                'question' => '請描述您與申請人的關係',
                'type' => 'textarea',
                'required' => true,
                'max_length' => 500,
            ],
            [
                'id' => 'academic_performance',
                'question' => '請評估申請人的學術表現',
                'type' => 'textarea',
                'required' => true,
                'max_length' => 1000,
            ],
            [
                'id' => 'research_potential',
                'question' => '請評估申請人的研究潛力',
                'type' => 'textarea',
                'required' => true,
                'max_length' => 1000,
            ],
            [
                'id' => 'recommendation_strength',
                'question' => '您推薦此申請人的程度',
                'type' => 'select',
                'required' => true,
                'options' => ['強烈推薦', '推薦', '有條件推薦', '不推薦'],
            ],
            [
                'id' => 'additional_comments',
                'question' => '其他補充說明',
                'type' => 'textarea',
                'required' => false,
                'max_length' => 1000,
            ],
        ];

        return QuestionnaireTemplate::create([
            'department_name' => 'default',
            'program_type' => 'default',
            'template_name' => '預設推薦問卷',
            'questions' => $basicQuestions,
            'is_active' => true,
        ]);
    }
}
